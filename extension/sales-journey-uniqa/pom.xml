<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zatech.genesis</groupId>
		<artifactId>sales-journey-extension</artifactId>
		<version>${genesisVersion}</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>sales-journey-uniqa</artifactId>
	<version>${genesisVersion}</version>
	<packaging>jar</packaging>
	<name>sales-journey-uniqa</name>
	<description>Sales Journey Uniqa Project</description>

	<dependencies>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>sales-journey-client-biz-auto</artifactId>
			<version>${project.version}</version>
		</dependency>
		<dependency>
			<groupId>com.zhongan</groupId>
			<artifactId>octopus-spring-boot-starter-rpc-openfeign</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-exception</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>swagger-core-jakarta</artifactId>
					<groupId>io.swagger.core.v3</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-share</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan</groupId>
			<artifactId>zatech-resourcecode-enum-gaia</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>lowcode-framework-client-sdk</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>lowcode-framework-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis.portal</groupId>
			<artifactId>toolbox-jsonschema</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-market-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-pos-online-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.pluginframework</groupId>
			<artifactId>plugin-api</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.pluginframework</groupId>
			<artifactId>plugin-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.zhongan.graphene</groupId>
			<artifactId>zatech-policy-api</artifactId>
			<version>${genesisVersion}</version>
		</dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>sales-journey-client-biz-common</artifactId>
			<version>${genesisVersion}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>sales-journey-integration</artifactId>
            <version>${genesisVersion}</version>
        </dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>sales-journey-policy-record-api</artifactId>
			<version>${genesisVersion}</version>
		</dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>sales-journey-recall-api</artifactId>
			<version>${genesisVersion}</version>
		</dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>6.4.4.Final</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.zatech.genesis</groupId>
			<artifactId>sales-journey-infra</artifactId>
			<version>${genesisVersion}</version>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>${thoughtworks-version}</version>
		</dependency>
	</dependencies>
</project>
