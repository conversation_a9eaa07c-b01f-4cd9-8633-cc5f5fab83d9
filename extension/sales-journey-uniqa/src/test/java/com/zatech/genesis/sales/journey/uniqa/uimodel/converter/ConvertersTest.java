/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.uimodel.converter;

import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.share.common.ModelObjectConverter;

import java.util.Arrays;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ConvertersTest {

    public static class BaseType {

        public void baseCalled() {
        }

        public void derivedCalled() {
        }

    }

    public static class BaseTypeConverter implements ModelObjectConverter<BaseType, Object, Object> {

        @Override
        public Object convert(BaseType model, Object object, Object context) {
            model.baseCalled();
            return object;
        }

    }

    public static class DerivedType extends BaseType {
    }

    public static class DerivedTypeConverter implements ModelObjectConverter<DerivedType, Object, Object> {

        @Override
        public Object convert(DerivedType model, Object object, Object context) {
            model.derivedCalled();
            return object;
        }

    }

    @Test
    void givenBaseType_whenConvert_thenConverterForDerivedMethodCalled() {
        var converters = new Converters(
            Arrays.asList(new BaseTypeConverter(), new DerivedTypeConverter())
        );

        BaseType model = Mockito.mock(DerivedType.class);

        converters.convert(model, new Object(), new Object());

        Mockito.verify(model, Mockito.never()).baseCalled();
        Mockito.verify(model, Mockito.times(1)).derivedCalled();
    }

    public static class BaseTypeInDstConverter implements ModelObjectConverter<Object, BaseType, Object> {

        @Override
        public BaseType convert(Object object, BaseType model, Object context) {
            model.baseCalled();
            return model;
        }

    }

    public static class DerivedTypInDstConverter implements ModelObjectConverter<Object, DerivedType, Object> {

        @Override
        public DerivedType convert(Object object, DerivedType model, Object context) {
            model.derivedCalled();
            return model;
        }

    }

    @Test
    void givenBaseTypeInDst_whenConvert_thenConverterForDerivedMethodCalled() {
        var converters = new Converters(
            Arrays.asList(new BaseTypeInDstConverter(), new DerivedTypInDstConverter())
        );

        BaseType model = Mockito.mock(DerivedType.class);

        converters.convert(new Object(), model, new Object());

        Mockito.verify(model, Mockito.never()).baseCalled();
        Mockito.verify(model, Mockito.times(1)).derivedCalled();
    }

}