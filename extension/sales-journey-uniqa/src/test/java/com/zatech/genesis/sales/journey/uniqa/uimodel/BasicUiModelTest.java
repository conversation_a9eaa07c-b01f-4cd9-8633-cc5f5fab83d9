/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.uimodel;

import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;

import java.lang.reflect.Field;
import java.util.Arrays;

import lombok.SneakyThrows;

import org.junit.jupiter.api.Test;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;

class BasicUiModelTest {

    @Test
    void givenBasicUiModel_whenGetAllGoods_thenReturnAll() {
        var basicUiModel = new BasicUiModel();

        var expectedAutoGoods = Arrays.stream(BasicUiModel.class.getDeclaredFields())
            .filter(f -> AutoGoods.class == f.getType())
            .map(f -> {
                var autoProduct = (AutoGoods) fieldGet(f, basicUiModel);
                autoProduct = newIfNull(autoProduct);
                fieldSet(f, basicUiModel, autoProduct);
                return autoProduct;
            })
            .toList();

        var allGoods = basicUiModel.getAllGoods();

        assertThat(allGoods).containsExactlyInAnyOrderElementsOf(expectedAutoGoods);
    }

    @SneakyThrows
    private Object fieldGet(Field f, Object target) {
        f.setAccessible(true);
        return f.get(target);
    }

    @SneakyThrows
    private void fieldSet(Field f, Object target, Object value) {
        f.setAccessible(true);
        f.set(target, value);
    }

    private AutoGoods newIfNull(AutoGoods autoGoods) {
        if (autoGoods == null) {
            return new AutoGoods();
        }
        return autoGoods;
    }

}