sj.file.on.renewing.policy=Obnavljate svoju polisu, pa nije potrebno da se prenose fajlovi.
sj.file.date.not.found=Datum nije pronađen, molimo proverite.
sj.file.sub.ui.model.not.found=Podmodel korisničkog interfejsa nije pronađen.
sj.file.attachment.is.missing=<PERSON>ao nam je, vaš prilog nedostaje.
sj.auto.vehicle.data.not.match=<PERSON>ao nam je, podaci o vozilu se ne podudaraju sa grupom vozila, molimo proverite ponovo.
common.google.map.extract.type.is.null=Greška pri pozivanju Google Maps.
common.google.map.extractor.not.found=Nepodržana vrsta upita za Google Maps.
sj.biz.leads.form.sys.fail=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokušajte ponovo kasnije.
sj.web.file.upload.error=Neuspešno slanje fajla.
auth.verification.code.expired=Verifikacioni kod je istekao. Molimo dobijte novi verifikacioni kod.
auth.verification.code.not.match=Pogrešan verifikacioni kod. Molimo proverite.
auth.verification_bad_captcha=Pogrešan grafički verifikacioni kod. Molimo proverite.
auth.notification_rule_code_not_found=Nije pronađena obaveštenje.
sj.policy.order.confirm.judge.order.status.not.unfinished=Žao nam je, porudžbina je već kreirana, ono što ste izmenili neće biti sačuvano za ovu porudžbinu.
sj.policy.issuance.status.is.illegal=Predlog nije validan.
sj.biz.policy.sys.fail=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokušajte ponovo kasnije.
ap.service.cancel.policy.after.14.days=Nije dozvoljeno pokretanje zahteva za otkazivanje na Agent Portalu duže od 14 dana. Molimo kontaktirajte osoblje pozadine.
sj.auto.promotion.code.not.valid=Promotivni kod nije validan.
sj.biz.quotation.sys.fail=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.biz.renew.quotation.sys.fail=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.file.sub.order.not.found=Sistemski error, molimo kontaktirajte IT službu.
sj.file.pending.case.not.found=Žao nam je, nema validnog zadatka za praćenje, molimo pokušajte kasnije.
sj.file.pending.case.is.close=Žao nam je, zadatak za praćenje je već zatvoren. Ne možete prenositi dokumente.
sj.file.order.not.found=Porudžbina nije pronađena, molimo proverite.
sj.file.policy.record.not.found=Zapis o polisi nije pronađen, molimo proverite.
sj.issuance.mtpl.rider.issuance.check=Molimo obratite pažnju da bi predlog MTPL dodatka trebalo izdati nakon što se izda povezani predlog MTPL-a.
sj.issuance.pa.issuance.check=Potrebno je izdati ponudu za polisu autoodgovornosti pre izdavanja ponude za polisu obaveznog osiguranja putnika.
sj.domain.pay.order.not.found=Informacije o plaćanju nisu pronađene.
sj.payment.create.payment.error=Nije uspelo kreiranje plaćanja.
sj.payment.cancel.payment.error=Nije uspelo otkazivanje plaćanja.
sj.domain.pay.order.status.is.not.payed=Status plaćanja nije plaćeno.
sj.domain.order.recall.not.found=Zapis o povratu nije pronađen.
sj.domain.create.recall.fail=Nije uspelo kreiranje informacija o povratu.
sj.domain.check.order.recall.email.fail=Molimo koristite email osiguranika da biste ušli u proces kupovine.
sj.domain.order.payment.judge.order.status.payed=Plaćanje ne može biti završeno, porudžbina je već plaćena.
sj.domain.order.payment.amount.incorrect=Plaćanje ne može biti završeno jer su iznosi netačni.
sj.policy.out.service.cdc.search.policy.new.list.error=Žao nam je, došlo je do greške pri pretraživanju polisa. Molimo pokušajte ponovo.
sj.policy.out.service.int.gateway.query.mhull.last.policy.error=Izvinjavamo se, došlo je do greške pri pretraživanju Mhull polisa. Molimo pokušajte ponovo.
sj.policy.out.service.int.gateway.query.mtpl.last.policy.error=Izvinjavamo se, došlo je do greške pri pretraživanju polisa Motorne odgovornosti trećeg lica. Molimo pokušajte ponovo.
sj.auto.rule.engine.execute.batch.fire.error=Izvinjavamo se, trenutno imamo problem sa sistemom. Molimo pokušajte ponovo.
sj.auto.out.service.search.policy.last.error=Izvinjavamo se, došlo je do greške pri pozivanju spoljnog servisa. Molimo pokušajte ponovo.
sj.auto.external.coverage.check.get.vin.or.oib.error=Izvinjavamo se, došlo je do greške pri dobijanju Vin broja ili OIB-a. Molimo proverite.
sj.market.goods.not.found=roba nije pronađena, ID robe: {0}
sj.file.upload.format.not.supported=Format fajla nije podržan.
sj.file.upload.bad.content=Sadržaj fajla je neispravan.
sj.file.upload.file.too.large=Fajl je prevelik.
sj.file.upload.filename.too.long=Naziv fajla je predugačak.
sj.file.upload.unknown=Došlo je do interne greške u sistemu.
sj.domain.order.payment.judge.order.status.not.confirm=Plaćanje nije moguće završiti, status porudžbine nije potvrđen.
policy.not.found=Izvinjavamo se, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
policy.status.cancel=Izvinjavamo se, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
policy.no.unmatched=Izvinjavamo se, ova polisa ne ispunjava kriterijume za obnovu, možete koristiti broj polise [%s] za obnovu.
policy.goods.unmatched=Izvinjavamo se, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.check.failed=Izvinjavamo se, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.expiry.data.check.failed=Izvinjavamo se, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.expiry.duplicate.mtpl=Žao nam je, klijent ne može kupiti MTPL polisu jer već ima važeću polisu, molimo proverite ponovo.
renewal.expiry.duplicate.mhull=Žao nam je, klijent ne može kupiti Mhull polisu jer već ima važeću polisu, molimo proverite ponovo.
sj.masterpolicy.rider.config.error=Prema trenutnoj definiciji master plana, {0} je istovremeno višestruko povezan sa Motor Casco i MTPL polisom. Molimo kontaktirajte osiguravača kako biste proverili definiciju master plana.
sj.issuance.issuance.status.is.withdrawn.pa=Izvinjavamo se, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mtpl.rider=Izvinjavamo se, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mtpl=Izvinjavamo se, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mhull=Izvinjavamo se, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.effective=Predlog je nevažeći, molimo kontaktirajte korisničku službu ili agenta.
sj.issuance.issuance.status.is.no.valid=Predlog je već izdao polisu. Molimo vas da je ne izdajete ponovo.
sj.ocr.ocr.failed=Neuspešno prepoznavanje OCR.
sj.auto.coverage.check.error=Nažalost, došlo je do izuzetka u usluzi, molimo pokušajte ponovo.
sj.domain.ui.model.existed=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokrenite novu porudžbinu.
sj.domain.ui.model.is.changed=Osvežite veb stranicu jer su informacije o porudžbini ažurirane.
sj.domain.ui.model.not.found=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokrenite novu porudžbinu.
sj.domain.order.not.found=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokrenite novu porudžbinu.
sj.domain.no.ui.model=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokrenite novu porudžbinu.

sj.biz.quotation.sys.error=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.biz.renew.quotation.sys.error=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.market.calculator.rate.table.not.match=Postoji nevažeći parametar u citatu, molimo proverite.
#uniqa only
sj.uniqa.waiting.for.payment=Ovaj predlog je već potvrđen, molimo sačekajte uplatu.
sj.uniqa.issuing.policy=Sistem izdaje polise, molimo sačekajte neko vreme.

system.remote.call.error=Sistemski error, molimo kontaktirajte IT službu.
system.internal.server.error=Sistemski error, molimo kontaktirajte IT službu.
system.null.pointer=Sistemski error, molimo kontaktirajte IT službu.
system.http.request.method.not.supported=Sistemski error, molimo kontaktirajte IT službu.
system.http.media.type.not.supported=Sistemski error, molimo kontaktirajte IT službu.
system.http.media.type.not.acceptable=Sistemski error, molimo kontaktirajte IT službu.
system.missing.path.variable=Sistemski error, molimo kontaktirajte IT službu.
system.request.param.invalid=Sistemski error, molimo kontaktirajte IT službu.
system.type.mismatch=Sistemski error, molimo kontaktirajte IT službu.
system.validation.failed=Sistemski error, molimo kontaktirajte IT službu.
system.http.message.not.readable=Sistemski error, molimo kontaktirajte IT službu.
system.http.message.not.writable=Sistemski error, molimo kontaktirajte IT službu.
system.tenant.not.found=Sistemski error, molimo kontaktirajte IT službu.
system.no.handler.found=Sistemski error, molimo kontaktirajte IT službu.
system.request.timeout=Sistemski error, molimo kontaktirajte IT službu.
system.argument.validation.failed=Sistemski error, molimo kontaktirajte IT službu.
system.data.access.invalid=Sistemski error, molimo kontaktirajte IT službu.
sj.client.order.not.found=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokrenite novu porudžbinu.
sj.issuance.current.proposal.has.locked=Trenutni predlog obra?uju korisnici iz pozadine, molimo vas da proverite najnovije informacije.
sj.uniqa.disability.discount=Kupac je već podneo zahtev za popust na osnovu invaliditeta, molim vas obrišite.
sj.uniqa.disability.discount.company=Oba osiguravača i osigurana lica su pravna lica, ne mogu imati popust za invalidnost.
sj.uniqa.disability.discount.passport=Molim vas, koristite JMBG ako želite da podnesete zahtev za popust na osnovu invaliditeta.

sj.issuance.proposal.no.is.empty=Sistemski error, molimo kontaktirajte IT službu.
sj.issuance.rule.check.result.null=Sistemski error, molimo kontaktirajte IT službu.

sj.leads.leads.form.submit.limit=Podno?enje obrasca za leadove je prema?ilo limit, molimo poku?ajte ponovo kasnije.
sj.uos_nbo.validation.error.code={0}

sj.renewal.renew.verify.auth.error=?ao mi je, ova polisa pripada drugom agentu, ne mo?ete je obnoviti.

sj.policy.not.mtpl.policy=Molim vas, uvedite pravu MTPL politiku ne.

sj.issuance.order.already.confirmed=Žao nam je, porudžbina je već potvrđena, Molimo vas da se vratite na agentski portal kako biste izmenili originalni predlog.

