sj.market.goods.not.found=goods not found, goods id: {0}
sj.file.upload.format.not.supported=Format of file is not supported.
sj.file.upload.bad.content=File content is malformed.
sj.file.upload.file.too.large=File is too large.
sj.file.upload.filename.too.long=File name is too long.
sj.file.upload.unknown=System occurs an internal error.
sj.domain.order.payment.judge.order.status.not.confirm=Payment cannot be completed, order status is not confirm.
policy.not.found=Sorry the policy doesn't meet the renewal criteria. Please issue a new policy.
policy.status.cancel=Sorry the policy doesn't meet the renewal criteria. Please issue a new policy.
policy.no.unmatched=Sorry this policy doesn't meet the renewal criteria, you can use policy no. [%s] to renew.
policy.goods.unmatched=Sorry the policy doesn't meet the renewal criteria. Please issue a new policy.
renewal.check.failed=Sorry the policy doesn't meet the renewal criteria. Please issue a new policy.
sj.ocr.ocr.failed=OCR recognition failed.
renewal.expiry.data.check.failed=Sorry the policy doesn't meet the renewal criteria. Please issue a new policy.
renewal.expiry.duplicate.mtpl=Sorry the client cannot buy MTPL policy since there is a policy in effective, please check again.
renewal.expiry.duplicate.mhull=Sorry the client cannot buy Mhull policy since there is a policy in effective, please check again.
renewal.use.last.year.policy.no.check.failed=This policy was renewed to [%s].Please use last year policy no. [%s] to renew.
sj.masterpolicy.rider.config.error=According to the current definition of master plan, {0} is attached to Motor Casco and MTPL repeatedly at the same time. Please contact the underwriter to check the master plan definition.
sj.issuance.issuance.status.is.withdrawn.pa= Sorry the issuance has been withdrawn, please try again
sj.issuance.issuance.status.is.withdrawn.mtpl.rider= Sorry the issuance has been withdrawn, please try again
sj.issuance.issuance.status.is.withdrawn.mtpl= Sorry the issuance has been withdrawn, please try again
sj.issuance.issuance.status.is.withdrawn.mhull= Sorry the issuance has been withdrawn, please try again
sj.issuance.issuance.status.is.effective=The proposal has already issued a policy. Please do not issue it again.
sj.issuance.issuance.status.is.no.valid=The proposal is invalid, please contact customer service or agent.
sj.file.on.renewing.policy=You are renewing your policy, so no file need to be uploaded.
sj.file.date.not.found=Date not found, please check
sj.file.sub.ui.model.not.found=sub ui model not found
sj.file.attachment.is.missing=Sorry, your attachment is missing.
sj.auto.vehicle.data.not.match=Sorry the vehicle data is not matching with the vehicle group, please check again.
##-------------------------------------------------------------------
common.google.map.extract.type.is.null=An error in calling Google Maps.
common.google.map.extractor.not.found=Unsupported query type of Google Maps.
sj.biz.leads.form.sys.fail=Sorry, an exception occurred in service. Please try again later.
sj.web.file.upload.error=File upload failed.
auth.verification.code.expired=The verification code has expired. Please obtain a new verification code.
auth.verification.code.not.match=Wrong verification code. Please check.
auth.verification_bad_captcha=Wrong graphic verification code. Please check.
auth.notification_rule_code_not_found=No notification found.
sj.policy.order.confirm.judge.order.status.not.unfinished=Sorry, the order is already created, what you edited will not be saved for this order.
sj.policy.issuance.status.is.illegal=The proposal is not valid.
sj.biz.policy.sys.fail=Sorry, an exception occurred in service. Please try again later.
ap.service.cancel.policy.after.14.days=It is not allowed to initiate a cancellation application on the Agent Portal for more than 14 days. Please contact the backend staff.
sj.auto.promotion.code.not.valid=Promotion code is invalid.
sj.biz.quotation.sys.fail=Something wrong in quotation, please try agian.
sj.biz.renew.quotation.sys.fail=Something wrong in quotation, please try agian.
sj.file.sub.order.not.found=System error, please contact IT service.
sj.file.pending.case.not.found=Sorry, there is not valid follow-up task, please try later.
sj.file.pending.case.is.close=Sorry, the follow-up task has been closed already. You cannot upload documents.
sj.file.order.not.found=Order not found, please check.
sj.file.policy.record.not.found=Policy record not found, please check.
sj.issuance.mtpl.rider.issuance.check=Please note that the MTPL rider proposal should be issued after the associated proposal of MTPL is issued.
sj.issuance.pa.issuance.check=Please note that the pa proposal should be issued after the associated proposal of MTPL is issued.
sj.domain.pay.order.not.found=Pay info not found.
sj.payment.create.payment.error=Failed to create a payment.
sj.payment.cancel.payment.error=Failed to cancel the payment.
sj.domain.pay.order.status.is.not.payed=The payment status is not paid.
sj.domain.order.recall.not.found=Recall record not found.
sj.domain.create.recall.fail=Failed to create recall information.
sj.domain.check.order.recall.email.fail=Please use policyholder email to enter purchase process.
sj.domain.order.payment.judge.order.status.payed=Payment cannot be completed, order has already been paid.
sj.domain.order.payment.amount.incorrect=Payment cannot be completed because the amounts are incorrect.
sj.policy.out.service.cdc.search.policy.new.list.error=Sorry, something wrong in querying policies. Please try again.
sj.policy.out.service.int.gateway.query.mhull.last.policy.error=Sorry, something wrong in querying Mhull policies. Please try again.
sj.policy.out.service.int.gateway.query.mtpl.last.policy.error=Sorry, something wrong in querying policies of Motor Third Party Liability. Please try again.
sj.auto.rule.engine.execute.batch.fire.error=Sorry, we are experiencing a system disruption. Please try again.
sj.auto.out.service.search.policy.last.error=Sorry, something wrong in calling external service. Please try again.
sj.auto.external.coverage.check.get.vin.or.oib.error=Sorry, something error in get Vin No. or OIB. Please check.
sj.auto.coverage.check.error=Sorry, an exception occurred in service, please try agian.
sj.domain.ui.model.existed=Sorry, an exception occurred in service. Please initiate a new order.
sj.domain.ui.model.is.changed=Please refresh the webpage as the order information has been updated.
sj.domain.ui.model.not.found=Sorry, an exception occurred in service. Please initiate a new order.
sj.domain.order.not.found=Sorry, an exception occurred in service. Please initiate a new order.
sj.domain.no.ui.model=Sorry, an exception occurred in service. Please initiate a new order.

sj.biz.quotation.sys.error=Something wrong in quotation, please try agian.
sj.biz.renew.quotation.sys.error=Something wrong in quotation, please try agian.
sj.market.calculator.rate.table.not.match=There is an illegal parameter in the quotation, please check.
#uniqa only
sj.uniqa.waiting.for.payment=This proposal has confirmed already, please waiting for payment.
sj.uniqa.issuing.policy=System is issuing policies, please wait for some time.

sj.leads.leads.form.submit.limit=Lead form submission exceeded limit, please try again later.

sj.renewal.renew.verify.auth.error=Sorry, this policy belongs to another agent, you cannot renew it.

sj.policy.not.mtpl.policy=Please input the correct MTPL policy no.

system.remote.call.error=System error, please contact IT service.
system.internal.server.error=System error, please contact IT service.
system.null.pointer=System error, please contact IT service.
system.http.request.method.not.supported=System error, please contact IT service.
system.http.media.type.not.supported=System error, please contact IT service.
system.http.media.type.not.acceptable=System error, please contact IT service.
system.missing.path.variable=System error, please contact IT service.
system.request.param.invalid=System error, please contact IT service.
system.type.mismatch=System error, please contact IT service.
system.validation.failed=System error, please contact IT service.
system.http.message.not.readable=System error, please contact IT service.
system.http.message.not.writable=System error, please contact IT service.
system.tenant.not.found=System error, please contact IT service.
system.no.handler.found=System error, please contact IT service.
system.request.timeout=System error, please contact IT service.
system.argument.validation.failed=System error, please contact IT service.
system.data.access.invalid=System error, please contact IT service.
sj.client.order.not.found=Sorry, an exception occurred in service. Please initiate a new order.
sj.issuance.current.proposal.has.locked=Current proposal is handled by backend users, please check the latest information.
sj.uniqa.disability.discount=The customer has already applied for disability discount, please delete.
sj.uniqa.disability.discount.company=Both policyholder and insured are legal entities, cannot have disability discount.
sj.uniqa.disability.discount.passport=Please use JMBG if you want to apply disability discount.

sj.issuance.proposal.no.is.empty=System error, please contact IT service.
sj.issuance.rule.check.result.null=System error, please contact IT service.
sj.issuance.order.already.confirmed=Sorry the order is already confirmed, please come back to agent portal to edit the original proposal.
sj.uos_nbo.validation.error.code={0}
