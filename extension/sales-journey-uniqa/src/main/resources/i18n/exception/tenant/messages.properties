sj.file.sub.order.not.found=Sub order not found, please check
sj.file.pending.case.not.found=Sorry, we are experiencing a system disruption. Please try again.
sj.file.pending.case.is.close=Sorry, the pending case has been closed already. You cannot upload documents again.
sj.file.on.renewing.policy=You are renewing your policy, so no file need to be uploaded.
sj.file.date.not.found=Date not found, please check
sj.file.sub.ui.model.not.found=sub ui model not found
sj.file.policy.record.not.found=Policy record not found, please check
sj.file.order.not.found=Order not found, please check
sj.file.attachment.is.missing=Sorry, your attachment is missing.

sj.auto.promotion.code.not.valid=Promotion code is invalid.
sj.auto.vehicle.data.not.match=Sorry the vehicle data is not matching with the vehicle group, please check again.

sj.issuance.mtpl.rider.issuance.check=Please note that the MTPL rider proposal should be issued after the associated proposal of MTPL is issued.

sj.auto.rule.engine.execute.batch.fire.error=Rule engine execute batch fire error.
sj.auto.out.service.search.policy.last.error=Out service search last policy error.
sj.auto.external.coverage.check.get.vin.or.oib.error=Coverage check get vin or oib error.
sj.auto.coverage.check.error=Sorry, an exception occurred in service, please try agian.

sj.policy.out.service.cdc.search.policy.new.list.error=Cdc search policy error.
sj.policy.out.service.int.gateway.query.mhull.last.policy.error=Int gateway query mhull error.
sj.policy.out.service.int.gateway.query.mtpl.last.policy.error=Int gateway query mtpl error.

sj.biz.quotation.sys.error=Something wrong in quotation, please try agian.
sj.biz.renew.quotation.sys.error=Something wrong in quotation, please try agian.
sj.market.calculator.rate.table.not.match=There is an illegal parameter in the quotation, please check.
#uniqa only
sj.uniqa.waiting.for.payment=This proposal has confirmed already, please waiting for payment.
sj.uniqa.issuing.policy=System is issuing policies, please wait for some time.

sj.leads.leads.form.submit.limit=Lead form submission exceeded limit, please try again later.

sj.renewal.renew.verify.auth.error=Sorry, this policy belongs to another agent, you cannot renew it.

sj.policy.not.mtpl.policy=Please input the correct MTPL policy no.

system.remote.call.error=System error, please contact IT service.
system.internal.server.error=System error, please contact IT service.
system.null.pointer=System error, please contact IT service.
system.http.request.method.not.supported=System error, please contact IT service.
system.http.media.type.not.supported=System error, please contact IT service.
system.http.media.type.not.acceptable=System error, please contact IT service.
system.missing.path.variable=System error, please contact IT service.
system.request.param.invalid=System error, please contact IT service.
system.type.mismatch=System error, please contact IT service.
system.validation.failed=System error, please contact IT service.
system.http.message.not.readable=System error, please contact IT service.
system.http.message.not.writable=System error, please contact IT service.
system.tenant.not.found=System error, please contact IT service.
system.no.handler.found=System error, please contact IT service.
system.request.timeout=System error, please contact IT service.
system.argument.validation.failed=System error, please contact IT service.
system.data.access.invalid=System error, please contact IT service.
sj.uos_nbo.validation.error.code={0}