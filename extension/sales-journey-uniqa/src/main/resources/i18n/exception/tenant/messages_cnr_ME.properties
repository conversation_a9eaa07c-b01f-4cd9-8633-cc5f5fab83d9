sj.file.on.renewing.policy=Obnavljate svoju polisu, pa nije potrebno da se prenose fajlovi.
sj.file.date.not.found=Datum nije pronađen, molimo provjerite.
sj.file.sub.ui.model.not.found=Podmodel korisničkog interfejsa nije pronađen.
sj.file.attachment.is.missing=<PERSON>ao nam je, vaš prilog nedostaje.
sj.auto.vehicle.data.not.match=<PERSON>ao nam je, podaci o vozilu se ne podudaraju sa grupom vozila, molimo provjerite ponovo.
common.google.map.extract.type.is.null=Greška pri pozivanju Google Maps.
common.google.map.extractor.not.found=Nepodržana vrsta upita za Google Maps.
sj.biz.leads.form.sys.fail=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokušajte ponovo kasnije.
sj.web.file.upload.error=Neusp<PERSON>lo slanje fajla.
auth.verification.code.expired=Verifikacioni kod je istekao. Molimo dobijte novi verifikacioni kod.
auth.verification.code.not.match=Pogrešan verifikacioni kod. Molimo provjerite.
auth.verification_bad_captcha=Pogrešan grafički verifikacioni kod. Molimo provjerite.
auth.notification_rule_code_not_found=Nije pronađena obavijest.
sj.policy.order.confirm.judge.order.status.not.unfinished=Žao nam je, narudžba je već kreirana, ono što ste izmijenili neće biti sačuvano za ovu narudžbu.
sj.policy.issuance.status.is.illegal=Prijedlog nije važeći.
sj.biz.policy.sys.fail=Žao nam je, došlo je do izuzetka u usluzi. Molimo pokušajte ponovo kasnije.
ap.service.cancel.policy.after.14.days=Nije dozvoljeno pokretanje zahtjeva za otkazivanje na Agent Portalu duže od 14 dana. Molimo kontaktirajte osoblje pozadine.
sj.auto.promotion.code.not.valid=Promotivni kod nije važeći.
sj.biz.quotation.sys.fail=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.biz.renew.quotation.sys.fail=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.file.sub.order.not.found=Sistemsko greška, molimo kontaktirajte IT službu.
sj.file.pending.case.not.found=Žao nam je, nema važećeg zadatka za praćenje, molimo pokušajte kasnije.
sj.file.pending.case.is.close=Žao nam je, zadatak za praćenje je već zatvoren. Ne možete prenositi dokumente.
sj.file.order.not.found=Narudžba nije pronađena, molimo provjerite.
sj.file.policy.record.not.found=Zapis o polisi nije pronađen, molimo provjerite.
sj.issuance.mtpl.rider.issuance.check=Molimo imajte na umu da bi prijedlog za MTPL dodatak trebao biti izdat nakon što je izdat povezani prijedlog za MTPL.
sj.issuance.pa.issuance.check=Potrebno je izdati ponudu za polisu autoodgovornosti prije izdavanja ponude za polisu obaveznog osiguranja putnika.
sj.domain.pay.order.not.found=Informacije o plaćanju nisu pronađene.
sj.payment.create.payment.error=Nije uspjelo kreiranje plaćanja.
sj.payment.cancel.payment.error=Nije uspjelo otkazivanje plaćanja.
sj.domain.pay.order.status.is.not.payed=Status plaćanja nije plaćeno.
sj.domain.order.recall.not.found=Zapis o povratu nije pronađen.
sj.domain.create.recall.fail=Nije uspjelo kreiranje informacija o povratu.
sj.domain.check.order.recall.email.fail=Molimo koristite e-mail nosioca polise da biste ušli u proces kupovine.
sj.domain.order.payment.judge.order.status.payed=Plaćanje ne može biti završeno, narudžba je već plaćena.
sj.domain.order.payment.amount.incorrect=Plaćanje ne može biti završeno jer su iznosi netačni.
sj.policy.out.service.cdc.search.policy.new.list.error=Žao mi je, došlo je do greške pri pretraživanju polisa. Molim vas pokušajte ponovo.
sj.policy.out.service.int.gateway.query.mhull.last.policy.error=Nažalost, došlo je do greške prilikom upita za Mhull polise. Molimo pokušajte ponovo.
sj.policy.out.service.int.gateway.query.mtpl.last.policy.error=Nažalost, došlo je do greške prilikom upita za polise Motorne odgovornosti trećeg lica. Molimo pokušajte ponovo.
sj.auto.rule.engine.execute.batch.fire.error=Nažalost, došlo je do prekida u sistemu. Molimo pokušajte ponovo.
sj.auto.out.service.search.policy.last.error=Nažalost, došlo je do greške prilikom pozivanja vanjske usluge. Molimo pokušajte ponovo.
sj.auto.external.coverage.check.get.vin.or.oib.error=Nažalost, došlo je do greške prilikom dobijanja Vin broja ili OIB-a. Molimo provjerite.
sj.market.goods.not.found=roba nije pronađena, ID robe: {0}
sj.file.upload.format.not.supported=Format datoteke nije podržan.
sj.file.upload.bad.content=Sadržaj datoteke je neispravan.
sj.file.upload.file.too.large=Datoteka je prevelika.
sj.file.upload.filename.too.long=Naziv datoteke je predugačak.
sj.file.upload.unknown=Došlo je do interne greške u sistemu.
sj.domain.order.payment.judge.order.status.not.confirm=Plaćanje nije moguće završiti, status narudžbe nije potvrđen.
policy.not.found=Nažalost, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
policy.status.cancel=Nažalost, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
policy.no.unmatched=Nažalost, ova polisa ne ispunjava kriterijume za obnovu, možete koristiti broj polise [%s] za obnovu.
policy.goods.unmatched=Nažalost, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.check.failed=Nažalost, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.expiry.data.check.failed=Nažalost, polisa ne ispunjava kriterijume za obnovu. Molimo izdajte novu polisu.
renewal.expiry.duplicate.mtpl=Žao nam je, klijent ne može kupiti MTPL polisu jer postoji važeća polisa, molimo provjerite ponovo.
renewal.expiry.duplicate.mhull=Žao nam je, klijent ne može kupiti Mhull polisu jer postoji važeća polisa, molimo provjerite ponovo.
sj.masterpolicy.rider.config.error=Prema trenutnoj definiciji master plana, {0} je istovremeno višestruko povezan sa Motor Casco i MTPL. Molimo kontaktirajte osiguravajućeg agenta da provjeri definiciju master plana.
sj.issuance.issuance.status.is.withdrawn.pa=Nažalost, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mtpl.rider=Nažalost, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mtpl=Nažalost, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.withdrawn.mhull=Nažalost, izdavanje je povučeno, molimo pokušajte ponovo
sj.issuance.issuance.status.is.effective=Predlog je nevažeći, molimo kontaktirajte korisničku službu ili agenta.
sj.issuance.issuance.status.is.no.valid=Predlog je već izdao polisu. Molimo vas da je ne izdajete ponovo.
sj.ocr.ocr.failed=Prepoznavanje OCR nije uspjelo.
sj.auto.coverage.check.error=Nažalost, došlo je do izuzetka u usluzi, molimo pokušajte ponovo.
sj.domain.ui.model.existed=Žao mi je, došlo je do izuzetka u usluzi. Molim vas pokrenite novu narudžbu.
sj.domain.ui.model.is.changed=Molim vas osvježite web stranicu jer su informacije o narudžbi ažurirane.
sj.domain.ui.model.not.found=Žao mi je, došlo je do izuzetka u usluzi. Molim vas pokrenite novu narudžbu.
sj.domain.order.not.found=Žao mi je, došlo je do izuzetka u usluzi. Molim vas pokrenite novu narudžbu.
sj.domain.no.ui.model=Žao mi je, došlo je do izuzetka u usluzi. Molim vas pokrenite novu narudžbu.

sj.biz.quotation.sys.error=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.biz.renew.quotation.sys.error=Nešto nije u redu sa ponudom, molimo pokušajte ponovo.
sj.market.calculator.rate.table.not.match=Postoji nevažeći parametar u citatu, molimo provjerite.
#uniqa only
sj.uniqa.waiting.for.payment=Ovaj prijedlog je već potvrđen, molimo sačekajte uplatu.
sj.uniqa.issuing.policy=Sistem izdaje polise, molimo sačekajte neko vrijeme.

system.remote.call.error=Sistemsko greška, molimo kontaktirajte IT službu.
system.internal.server.error=Sistemsko greška, molimo kontaktirajte IT službu.
system.null.pointer=Sistemsko greška, molimo kontaktirajte IT službu.
system.http.request.method.not.supported=Sistemsko greška, molimo kontaktirajte IT službu.
system.http.media.type.not.supported=Sistemsko greška, molimo kontaktirajte IT službu.
system.http.media.type.not.acceptable=Sistemsko greška, molimo kontaktirajte IT službu.
system.missing.path.variable=Sistemsko greška, molimo kontaktirajte IT službu.
system.request.param.invalid=Sistemsko greška, molimo kontaktirajte IT službu.
system.type.mismatch=Sistemsko greška, molimo kontaktirajte IT službu.
system.validation.failed=Sistemsko greška, molimo kontaktirajte IT službu.
system.http.message.not.readable=Sistemsko greška, molimo kontaktirajte IT službu.
system.http.message.not.writable=Sistemsko greška, molimo kontaktirajte IT službu.
system.tenant.not.found=Sistemsko greška, molimo kontaktirajte IT službu.
system.no.handler.found=Sistemsko greška, molimo kontaktirajte IT službu.
system.request.timeout=Sistemsko greška, molimo kontaktirajte IT službu.
system.argument.validation.failed=Sistemsko greška, molimo kontaktirajte IT službu.
system.data.access.invalid=Sistemsko greška, molimo kontaktirajte IT službu.
sj.client.order.not.found=Žao mi je, došlo je do izuzetka u usluzi. Molim vas pokrenite novu narudžbu.
sj.issuance.current.proposal.has.locked=Trenutni prijedlog obra?uju korisnici pozadinskog sistema, molimo provjerite najnovije informacije.
sj.uniqa.disability.discount.company=Osigurana lica i osiguratelji su pravna lica, ne mogu imati popust za invalidnost.
sj.uniqa.disability.discount.passport=Molim vas, koristite JMBG ako želite da podnesete zahtjev za popust na osnovu invaliditeta.
sj.uniqa.disability.discount=Kupac je već podnio zahtjev za popust na osnovu invaliditeta, molim vas obrišite.

sj.issuance.proposal.no.is.empty=Sistemsko greška, molimo kontaktirajte IT službu.
sj.issuance.rule.check.result.null=Sistemsko greška, molimo kontaktirajte IT službu.

sj.leads.leads.form.submit.limit=Podno?enje obrasca za leadove je prema?ilo limit, molimo poku?ajte ponovo kasnije.
sj.uos_nbo.validation.error.code={0}

sj.renewal.renew.verify.auth.error=?ao mi je, ova polisa pripada drugom agentu, ne mo?ete je obnoviti.

sj.policy.not.mtpl.policy=Predajte pravu politiku MTPL-a ne.

sj.issuance.order.already.confirmed=Žao nam je, porudžbina je već potvrđena. Molimo vas da se vratite na agentski portal kako biste izmijenili originalni prijedlog.
