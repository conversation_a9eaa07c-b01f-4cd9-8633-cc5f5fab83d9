coverage.check.mtpl.duplicate.d2a =Sorry the client cannot buy MTPL policy since there is a policy in effective, please check again.
coverage.check.mtpl.duplicate.d2c =Dear customer, sorry you already have effective MTPL policy, please check again.
coverage.check.mtpl.renew.d2a =The policy meets the renew criteria, please renew this policy.
coverage.check.mhull.duplicate.d2a =Sorry the client cannot buy Mhull policy since there is a policy in effective, please check again.
coverage.check.mhull.duplicate.d2c =Dear customer, sorry you already have effective Mhull policy, please check again.
coverage.check.mhull.renew.d2a =The policy meets the renew criteria, please renew this policy.
coverage.check.mhull.renew.d2c =Dear customer, you meet our Mhull policy's renew criteria, please contact your agent to renew your policy.
coverage.check.mhull.uw.d2c =You do not meet Mhull criteria.We will contact you later to help you to issue the Mhull policy.
coverage.check.pa.duplicate.d2a=Sorry the client cannot buy PA policy since there is a policy in effective, please check again.

coverage.check.mtpl.proceed.mhull.duplicate.d2c =Dear customer, sorry you cannot buy Mhull policy since you have a policy in effective, but you can continue with MTPL coverage.
coverage.check.mtpl.proceed.mhull.renew.d2c =Dear customer, you meet our Mhull policy's renew criteria, please contact your agent to renew your policy, but you can continue with MTPL coverage.
coverage.check.mtpl.proceed.mhull.uw.d2c =You do not meet Mhull criteria, you can continue to buy MTPL.We will contact you later to help you to issue the Mhull policy.
coverage.check.mtpl.duplicate.mhull.proceed.d2c =Dear customer, sorry you cannot buy MTPL policy since you have a policy in effective, but you can  continue with Mhull coverage.
coverage.check.mtpl.duplicate.mhull.duplicate.d2c =Dear customer, sorry you already have effective MTPL and Mhull policies, please check again.
coverage.check.mtpl.duplicate.mhull.renew.d2c =Dear customer, you meet our Mhull policy's renew criteria, please contact your agent to renew your policy, and we cannot offer you MTPL policy since you already have effective policy, please check again.
coverage.check.mtpl.duplicate.mhull.uw.d2c =Dear customer, sorry you cannot buy MTPL policy since you have a policy in effective, but if you want to buy Mhull policy, please leave your personal information and out agent will contact you as soon as possible.
coverage.check.mtpl.renew.mhull.proceed.d2c =Dear customer, you meet our MTPL policy's renew criteria, please contact your agent to renew your policy, but you can continue with Mhull coverage.
coverage.check.mtpl.renew.mhull.duplicate.d2c =Dear customer, you meet our MTPL policy's renew criteria, please contact your agent to renew your policy, and we cannot offer you Mhull policy since you already have effective policy, please check again.
coverage.check.mtpl.renew.mhull.renew.d2c =Dear customer, you meet our MTPL&Mhull policy's renew criteria, please contact your agent to renew your policy.
coverage.check.mtpl.renew.mhull.uw.d2c =Dear customer,  you meet our MTPL policy's renew criteria, please contact your agent to renew your policy, but if you want to buy Mhull policy, please leave your personal information and out agent will contact you as soon as possible.

coverage.check.mtpl.proceed.mhull.duplicate.d2a =Sorry the client cannot buy Mhull policy since there is a policy in effective, but you can continue with MTPL coverage.
coverage.check.mtpl.proceed.mhull.renew.d2a =The client meets our Mhull policy's renew criteria, please renew this policy, but you can continue with MTPL coverage.
coverage.check.mtpl.duplicate.mhull.proceed.d2a=Sorry the client already had effective MTPL, and Obligatory accident insurance for passengers also cannot be purchased, please check again. 
coverage.check.mtpl.renew.mhull.proceed.d2a=The client meets our MTPL policy's renew criteria, please renew this policy, and Obligatory Accident Insurance for passengers also cannot be purchased, but you can continue with Mhull coverage.
coverage.check.mtpl.duplicate.mhull.duplicate.d2a=Sorry the client already had effective MTPL and Mhull policy,and Obligatory accident insurance for passengers also cannot be purchased, please check again. 
coverage.check.mtpl.renew.mhull.duplicate.d2a=The client meets our MTPL policy's renew criteria, please renew this policy, and we cannot offer Mhull policy since the client already had effective policy, and Obligatory Accident Insurance for passengers also cannot be purchased, please check again. please check again.
coverage.check.mtpl.duplicate.mhull.renew.d2a=The client meets our Mhull policy's renew criteria, please renew this policy, and we cannot offer MTPL policy since the client already had an effective MTPL policy, and Obligatory Accident Insurance for passengers also cannot be purchased, please check again.
coverage.check.mtpl.renew.mhull.renew.d2a=The client meets our MTPL&Mhull policy's renew criteria, please renew the policy, and Obligatory Accident Insurance for passengers also cannot be purchased, please check again.

preview.coverage.check.mtpl.duplicate.d2c =Dear customer, sorry you already have effective MTPL policy, please check again.
preview.coverage.check.mtpl.duplicate.d2a =Sorry the client cannot buy MTPL policy since there is a policy in effective, please check again.
preview.coverage.check.mhull.duplicate.d2c =Dear customer, sorry you already have effective Mhull policy, please check again.
preview.coverage.check.mhull.duplicate.d2a =Sorry the client cannot buy Mhull policy since there is a policy in effective, please check again.

preview.coverage.check.mtpl.proceed.mhull.duplicate.d2c =Dear customer, sorry you already have effective Mhull policy, please go back to the Select Coverage page to choose MTPL only.
preview.coverage.check.mtpl.duplicate.mhull.proceed.d2c =Dear customer, sorry you already have effective MTPL policy, please go back to the Select Coverage page to choose Mhull only.
preview.coverage.check.mtpl.duplicate.mhull.duplicate.d2c =Dear customer, sorry you already have effective MTPL and Mhull policies, please check again.
preview.coverage.check.mtpl.proceed.mhull.duplicate.d2a =Sorry the client cannot buy Mhull policy since there is a policy in effective, please go back to the Select Coverage page to choose MTPL only.
preview.coverage.check.mtpl.duplicate.mhull.proceed.d2a =Sorry the client cannot buy MTPL policy since there is a policy in effective, please go back to the Select Coverage page to choose Mhull only.
preview.coverage.check.mtpl.duplicate.mhull.duplicate.d2a =Sorry the client cannot buy MTPL and Mhull policy since there are policies in effective, please check again.

coverage.check.mtpl.proceed.mhull.duplicate.pa.proceed.d2a =Sorry the client cannot buy Mhull policy since there is a policy in effective, but you can continue with MTPL coverage and Obligatory accident insurance for passengers.
coverage.check.mtpl.proceed.mhull.duplicate.pa.duplicate.d2a = Sorry the client cannot buy Mhull&Obligatory accident insurance for passengers policy since there is a policy in effective, but you can continue with MTPL coverage.
coverage.check.mtpl.proceed.mhull.renew.pa.proceed.d2a = The client meets our Mhull policy's renew criteria, please renew this policy, but you can continue with MTPL coverage and Obligatory accident insurance for passengers.
coverage.check.mtpl.proceed.mhull.renew.pa.duplicate.d2a = The client meets our Mhull policy's renew criteria, please renew this policy,and we cannot offer Obligatory accident insurance for passengers since client already had an effective policy, but you can continue with MTPL coverage.
coverage.check.mtpl.proceed.mhull.proceed.pa.duplicate.d2a = Sorry the client cannot buy Obligatory accident insurance for passengers since there is a policy in effective, but you can continue with  Mhull and MTPL coverage.
coverage.check.mtpl.duplicate.mhull.renew.pa.proceed.d2a = The client meets our Mhull policy's renew criteria, please renew this policy, and we cannot offer MTPL policy since the client already had effective policy, please check again.
coverage.check.mtpl.duplicate.mhull.renew.pa.duplicate.d2a = The client meets our Mhull policy's renew criteria, please renew this policy, and we cannot offer MTPL and PA policy since the client already had effective policy, please check again.
coverage.check.mtpl.duplicate.mhull.proceed.pa.proceed.d2a = Sorry the client cannot buy MTPL policy since there is a policy in effective, but you can continue with Mhull coverage.
coverage.check.mtpl.duplicate.mhull.proceed.pa.duplicate.d2a = Sorry the client cannot buy MTPL and PA policy since there is a policy in effective, but you can continue with Mhull coverage.
coverage.check.mtpl.duplicate.mhull.duplicate.pa.proceed.d2a = Sorry the client already had effective MTPL and Mhull policy, please check again.
coverage.check.mtpl.duplicate.mhull.duplicate.pa.duplicate.d2a = Sorry the client already had effective MTPL, Mhull and PA policy, please check again.
coverage.check.mtpl.renew.mhull.proceed.pa.proceed.d2a = The client meets our MTPL policy's renew criteria, please renew this policy, but you can continue with Mhull coverage.
coverage.check.mtpl.renew.mhull.proceed.pa.duplicate.d2a = The client meets our MTPL policy's renew criteria, please renew this policy, and we cannot offer PA policy since the client already had effective policy, but you can continue with Mhull coverage.
coverage.check.mtpl.renew.mhull.duplicate.pa.proceed.d2a = The client meets our MTPL policy's renew criteria, please renew this policy, and we cannot offer Mhull policy since the client already had effective policy, please check again.
coverage.check.mtpl.renew.mhull.duplicate.pa.duplicate.d2a = The client meets our MTPL policy's renew criteria, please renew this policy, and we cannot offer Mhull and PA policy since the client already had effective policy, please check again.
coverage.check.mtpl.renew.mhull.renew.pa.proceed.d2a = The client meets our MTPL&Mhull policy's renew criteria, please renew the policy.
coverage.check.mtpl.renew.mhull.renew.pa.duplicate.d2a = The client meets our MTPL&Mhull policy's renew criteria, please renew this policy, and we cannot offer PA policy since the client already had effective policy, please check again.

agent.check.error=This vehicle has an ongoing offer by another agent %s. Are you sure to continue to issue policy?


