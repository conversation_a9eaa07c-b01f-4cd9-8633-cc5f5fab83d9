{"paymentMethod": "BANK_SLIP", "generateQuickOffer": false, "E_IsDiplomat": false, "relationPolicyTemplateCode": "MTPL_Purchase_Rider_After_Main_Product", "commercialInsuranceGoods": {"goodsId": ***************, "goodsCode": "MotorHull", "selected": true, "isRenewalPolicy": true, "plan": {"planId": ***************, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "saCurrency": "EUR", "premiumCurrency": "EUR", "elements": {"nonStandardTariff": false}, "claimStack": {"Renting_Days_5": "0", "Rental_amount_limit": "0", "Renting_Days_10": "0", "MHLegalPretectionAmount_Higher": "0", "MHLegalPretectionAmount_Lower": "0", "MotorHullDeductiblePercentage": "0", "MotorHullDeductibleAmount": "0"}}}, "products": {"commercialInsuranceMainProduct": {"selected": false, "mainProduct": true, "productCode": "**********", "productId": ***************, "liabilities": {"trafficAccident": {"selected": true, "liabilityId": 803}, "glassBreakage": {"selected": true, "liabilityId": 811}, "theftRobberyVandalism": {"selected": true, "liabilityId": 821}, "flexa": {"selected": true, "liabilityId": 950114931720192}, "natCat": {"selected": true, "liabilityId": 950115200155649}, "otherCoverage": {"selected": true, "liabilityId": 950116508778498}}}, "trafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": 1215969347272704, "productCode": "100140", "liabilities": {"obligatoryMotorThirdPartyLiabilityForMaterialInjury": {"selected": true, "liabilityId": 1215989060501505, "sumInsured": "1000000"}, "obligatoryMotorThirdPartyLiabilityForMaterialDamage": {"selected": true, "liabilityId": 1215986510364672, "sumInsured": "200000"}}}, "roadsideAssistance": {"selected": false, "productId": 911455578767368, "productCode": "1901000040", "liabilities": {"montenegroOnlyRoadsideAssistance": {"selected": true, "liabilityId": 950354745245699}, "montenegroEuropeRoadsideAssistance": {"selected": false, "liabilityId": 950356037091332}}}, "legalProtection": {"selected": false, "productId": 911619391504384, "productCode": "1701000040", "liabilities": {"upTo4500LegalProtection": {"selected": false, "liabilityId": 981001014624256}, "upTo2000LegalProtection": {"selected": true, "liabilityId": 981001585049601}}}, "accident": {"selected": false, "productId": 911625582297089, "productCode": "0102030040", "liabilities": {"driverPassengerInjuryInVehicleAccident": {"selected": true, "sumInsured": "1000", "liabilityId": 831}, "driverPassengerDeathInVehicleAccident": {"selected": true, "sumInsured": "500", "liabilityId": 922614994337792}, "petsInjuryAccident": {"selected": true, "liabilityId": 1227199512330245}, "petsDeathAccident": {"selected": true, "liabilityId": 1227199982092294}}}, "tireInsurance": {"selected": false, "productId": 911630632239106, "productCode": "0300000042", "liabilities": {"tireInsurance": {"selected": true, "liabilityId": 832}}}, "compensationForCostsOfRentingACar": {"selected": false, "productId": 911635547963396, "productCode": "0300000040", "liabilities": {"compensationForCostsOfRentingACar": {"selected": true, "liabilityId": 985185134870529}}}, "machineryBreakage": {"selected": false, "productId": 1141892418912257, "productCode": "0300000041", "liabilities": {"insuranceOfWorkingMachineFromBreakage": {"selected": false, "liabilityId": 1222991987884032}, "insuranceOfVehicleInTotalFromBreakage": {"selected": true, "liabilityId": 1222993111957505}}}, "gapForLeasing": {"selected": false, "productId": 1693718373515264, "productCode": "1600000040", "liabilities": {"gapForLeasing": {"selected": true, "liabilityId": 1693745351278592}}}}, "vehicleInfo": {"E_VATRate": "0.21", "E_PPMV": "0", "machineUsageType": "Unknown", "vehicleOrigin": "4", "carOwner": {"customerType": "INDIVIDUAL"}}}