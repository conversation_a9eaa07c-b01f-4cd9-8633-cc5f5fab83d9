{"paymentMethod": "BANK_SLIP", "issueWithoutPayment": false, "E_IsDiplomat": false, "generateQuickOffer": false, "relationPolicyTemplateCode": "MTPL_Purchase_Rider_After_Main_Product", "trafficCompulsoryInsuranceGoods": {"goodsCode": "MTPL", "goodsId": ****************, "selected": true, "isRenewalPolicy": false, "plan": {"planId": ****************, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "15", "baseCurrency": "RSD", "saCurrency": "EUR", "premiumCurrency": "RSD", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "products": {"trafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": ****************, "productCode": "100140", "liabilities": {"obligatoryMotorThirdPartyLiabilityForMaterialInjury": {"selected": true, "liabilityId": ****************, "sumInsured": "1000000"}, "obligatoryMotorThirdPartyLiabilityForMaterialDamage": {"selected": true, "liabilityId": 1215986510364672, "sumInsured": "200000"}}}}, "vehicleInfo": {"vehicleGroup": "Passenger", "isNewVehicle": true, "E_VATRate": "0.2", "E_PPMV": "0", "plateType": "2", "vehicleLeasing": false, "isNotRegistered": false, "registrationArea": "Serbia", "machineUsageType": "Unknown", "claimExperience": {"claimNumberInLast5Year": 0, "claimNumberInLast3Year": 0, "claimNumberLastYear": 0, "lossRatioLastYear": "0"}, "carOwner": {"customerType": "INDIVIDUAL"}}}