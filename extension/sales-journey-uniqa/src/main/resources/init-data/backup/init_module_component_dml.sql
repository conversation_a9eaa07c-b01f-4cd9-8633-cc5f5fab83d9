INSERT INTO module_component (id, module_id, name, description, kind, component_type, data_mode,
                                               meta_info_extension, spec_info, component_data, component_data_format,
                                               tenant_code, gmt_created, creator, gmt_modified, modifier)
VALUES (***************, ***************, 'uniqa2MHullProductCorrelationMatrix',
        'this is uniqa2 MHull product correlation matrix routing table', 'DynamicComponent', 'RoutingTable', 'Auto',
        '{}', '{
    "extraInfo": {},
    "businessType": "MarketMatrixTable"
  }', null, null, 'uniqa', '2024-05-10 10:09:15', 'system', '2024-05-10 10:09:15', 'system');

INSERT INTO module_component (id, module_id, name, description, kind, component_type, data_mode,
                                               meta_info_extension, spec_info, component_data, component_data_format,
                                               tenant_code, gmt_created, creator, gmt_modified, modifier)
VALUES (***************, ***************, 'uniqa2MTPLProductCorrelationMatrix',
        'this is uniqa2 MTPL product correlation matrix routing table', 'DynamicComponent', 'RoutingTable', 'Auto',
        '{}', '{
    "extraInfo": {},
    "businessType": "MarketMatrixTable"
  }', null, null, 'uniqa', '2024-05-10 10:08:42', 'system', '2024-05-10 10:08:42', 'system');
