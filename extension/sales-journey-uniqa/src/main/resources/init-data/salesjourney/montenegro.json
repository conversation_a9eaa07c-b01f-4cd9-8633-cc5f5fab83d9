{"paymentMethod": "BANKART", "issueWithoutPayment": false, "E_IsDiplomat": false, "commercialInsuranceGoods": {"goodsId": ***************, "goodsCode": "MotorHull", "selected": true, "isRenewalPolicy": false, "plan": {"planId": ***************, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "saCurrency": "EUR", "premiumCurrency": "EUR", "claimStack": {"Renting_Days_5": "0", "Rental_amount_limit": "0", "Renting_Days_10": "0", "MHLegalPretectionAmount_Higher": "0", "MHLegalPretectionAmount_Lower": "0", "MotorHullDeductiblePercentage": "10", "MotorHullDeductibleAmount": "100"}, "elements": {"nonStandardTariff": false}}}, "trafficCompulsoryInsuranceGoods": {"goodsCode": "MTPL", "goodsId": ****************, "selected": true, "isRenewalPolicy": false, "plan": {"planId": ****************, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "saCurrency": "EUR", "premiumCurrency": "EUR", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "virtualTrafficCompulsoryInsuranceGoods": {"goodsCode": "MTPL_Riders", "goodsId": 1841750444359680, "isRenewalPolicy": false, "plan": {"planId": 1841756937142272, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "saCurrency": "EUR", "premiumCurrency": "EUR", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "products": {"commercialInsuranceMainProduct": {"mainProduct": true, "selected": true, "productCode": "0301000040", "productId": 911453179625479, "liabilities": {"trafficAccident": {"selected": true, "liabilityId": 803}, "glassBreakage": {"selected": true, "liabilityId": 811}, "theftRobberyVandalism": {"selected": true, "liabilityId": 821}, "flexa": {"selected": true, "liabilityId": 950114931720192}, "natCat": {"selected": true, "liabilityId": 950115200155649}, "otherCoverage": {"selected": true, "liabilityId": 950116508778498}}}, "trafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": 1215969347272704, "productCode": "100140", "liabilities": {"obligatoryMotorThirdPartyLiabilityForMaterialInjury": {"selected": true, "liabilityId": 1215989060501505, "sumInsured": "1000000"}, "obligatoryMotorThirdPartyLiabilityForMaterialDamage": {"selected": true, "liabilityId": 1215986510364672, "sumInsured": "200000"}}}, "virtualTrafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": 1836084845625346, "productCode": "Virtual"}, "passengerAccidentMainProduct": {"mainProduct": true, "selected": true, "productCode": "010701", "productId": 1836130680979459, "liabilities": {"deathDueToAccident": {"selected": true, "liabilityId": 530}, "disability": {"selected": true, "liabilityId": 698}, "medicalCostAndLoss": {"selected": true, "liabilityId": 1836136821440512}}}, "roadsideAssistance": {"selected": false, "productId": 911455578767368, "productCode": "**********", "liabilities": {"montenegroOnlyRoadsideAssistance": {"selected": true, "liabilityId": 950354745245699}, "montenegroEuropeRoadsideAssistance": {"selected": false, "liabilityId": 950356037091332}}}, "legalProtection": {"selected": false, "productId": 911619391504384, "productCode": "**********", "liabilities": {"upTo4500LegalProtection": {"selected": false, "liabilityId": 981001014624256}, "upTo2000LegalProtection": {"selected": true, "liabilityId": 981001585049601}}}, "accident": {"selected": false, "productId": 911625582297089, "productCode": "**********", "liabilities": {"driverPassengerInjuryInVehicleAccident": {"selected": true, "sumInsured": "1000", "liabilityId": 831}, "driverPassengerDeathInVehicleAccident": {"selected": true, "sumInsured": "500", "liabilityId": 922614994337792}, "petsInjuryAccident": {"selected": true, "liabilityId": 1227199512330245}, "petsDeathAccident": {"selected": true, "liabilityId": 1227199982092294}}}, "tireInsurance": {"selected": true, "productId": 911630632239106, "productCode": "0300000042", "liabilities": {"tireInsurance": {"selected": true, "liabilityId": 832}}}, "compensationForCostsOfRentingACar": {"selected": false, "productId": 911635547963396, "productCode": "0300000040", "liabilities": {"compensationForCostsOfRentingACar": {"selected": true, "liabilityId": 985185134870529}}}, "machineryBreakage": {"selected": false, "productId": 1141892418912257, "productCode": "0300000041", "liabilities": {"insuranceOfWorkingMachineFromBreakage": {"selected": false, "liabilityId": 1222991987884032}, "insuranceOfVehicleInTotalFromBreakage": {"selected": true, "liabilityId": 1222993111957505}}}, "gapForLeasing": {"selected": false, "productId": 1693718373515264, "productCode": "1600000040", "liabilities": {"gapForLeasing": {"selected": true, "liabilityId": 1693745351278592}}}, "glassBreakage": {"selected": false, "productId": 1254853850447873, "productCode": "030043", "liabilities": {"glassBreakageFixedCompensation1000Euro": {"selected": true, "liabilityId": 1227194462388226}, "glassBreakageFixedCompensation2000Euro": {"selected": false, "liabilityId": 1227195016036355}}}, "animalCollision": {"selected": false, "productId": 1254977934737411, "productCode": "030044", "liabilities": {"animalCollision": {"selected": true, "liabilityId": 1227196978970628}}}}, "vehicleInfo": {"vehicleGroup": "Passenger", "isNewVehicle": true, "vehicleInAdvertisingAndBranding": false, "E_VATRate": "0.21", "E_PPMV": "0", "plateType": "1", "vehicleLeasing": false, "isNotRegistered": false, "registrationArea": "Montenegro", "claimExperience": {"claimNumberInLast5Year": 0, "claimNumberInLast3Year": 0, "claimNumberLastYear": 0, "lossRatioLastYear": "0"}, "carOwner": {"customerType": "INDIVIDUAL"}}}