{"paymentMethod": "UPC", "issueWithoutPayment": false, "displayCurrency": "EUR", "E_IsDiplomat": false, "commercialInsuranceGoods": {"goodsId": 921476140122112, "goodsCode": "MotorHull", "selected": true, "isRenewalPolicy": false, "plan": {"planId": 921476643438592, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "baseCurrency": "RSD", "saCurrency": "EUR", "premiumCurrency": "EUR", "elements": {"salesCurrency": "EUR", "nonStandardTariff": false}, "claimStack": {"Renting_Days_5": "0", "Rental_amount_limit": "0", "Renting_Days_10": "0", "MHLegalPretectionAmount_Higher": "0", "MHLegalPretectionAmount_Lower": "0", "MotorHullDeductiblePercentage": "5", "MotorHullDeductibleAmount": "150"}}}, "trafficCompulsoryInsuranceGoods": {"goodsCode": "MTPL", "goodsId": 1217295619735552, "selected": true, "isRenewalPolicy": false, "plan": {"planId": 1217324174557184, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "baseCurrency": "RSD", "saCurrency": "EUR", "premiumCurrency": "RSD", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "virtualTrafficCompulsoryInsuranceGoods": {"goodsCode": "MTPL_Riders", "goodsId": 1831252923547649, "isRenewalPolicy": false, "plan": {"planId": 1831253527527425, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "baseCurrency": "RSD", "saCurrency": "EUR", "premiumCurrency": "EUR", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "products": {"commercialInsuranceMainProduct": {"selected": true, "mainProduct": true, "productCode": "0301000040", "productId": 911453179625479, "liabilities": {"trafficAccident": {"selected": true, "liabilityId": 803}, "glassBreakage": {"selected": true, "liabilityId": 811}, "theftRobberyVandalism": {"selected": true, "liabilityId": 821}, "flexa": {"selected": true, "liabilityId": 950114931720192}, "natCat": {"selected": true, "liabilityId": 950115200155649}, "otherCoverage": {"selected": true, "liabilityId": 950116508778498}}}, "trafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": 1215969347272704, "productCode": "100140", "liabilities": {"obligatoryMotorThirdPartyLiabilityForMaterialInjury": {"selected": true, "liabilityId": 1215989060501505, "sumInsured": "1000000"}, "obligatoryMotorThirdPartyLiabilityForMaterialDamage": {"selected": true, "liabilityId": 1215986510364672, "sumInsured": "200000"}}}, "virtualTrafficCompulsoryInsuranceMainProduct": {"mainProduct": true, "selected": true, "productId": 1826700442746881, "productCode": "Virtual"}, "passengerAccidentMainProduct": {"mainProduct": true, "selected": true, "productCode": "010801", "productId": 1823418332758017, "liabilities": {"deathDueToAccident": {"selected": true, "liabilityId": 530}, "disability": {"selected": true, "liabilityId": 698}, "medicalCostAndLoss": {"selected": true, "liabilityId": 1823430093586432}}}, "roadsideAssistance": {"selected": false, "productId": 911455578767368, "productCode": "**********", "liabilities": {"serbiaOnlyRoadsideAssistance": {"selected": true, "liabilityId": 950354745245699}, "serbiaEuropeRoadsideAssistance": {"selected": false, "liabilityId": 950356037091332}}}, "legalProtection": {"selected": false, "productId": 911619391504384, "productCode": "**********", "liabilities": {"upTo4500LegalProtection": {"selected": false, "liabilityId": 981001014624256}, "upTo2000LegalProtection": {"selected": true, "liabilityId": 981001585049601}}}, "accident": {"selected": false, "productId": 911625582297089, "productCode": "**********", "liabilities": {"driverPassengerInjuryInVehicleAccident": {"selected": true, "sumInsured": "1000", "liabilityId": 831}, "driverPassengerDeathInVehicleAccident": {"selected": true, "sumInsured": "500", "liabilityId": 922614994337792}, "petsInjuryAccident": {"selected": true, "liabilityId": 1227199512330245}, "petsDeathAccident": {"selected": true, "liabilityId": 1227199982092294}}}, "tireInsurance": {"selected": true, "productId": 911630632239106, "productCode": "0399000042", "liabilities": {"tireInsurance": {"selected": true, "liabilityId": 832}}}, "compensationForCostsOfRentingACar": {"selected": false, "productId": 911635547963396, "productCode": "0399000040", "liabilities": {"compensationForCostsOfRentingACar": {"selected": true, "liabilityId": 985185134870529}}}, "machineryBreakage": {"selected": false, "productId": 1141892418912257, "productCode": "0399000041", "liabilities": {"insuranceOfWorkingMachineFromBreakage": {"selected": false, "liabilityId": 1222991987884032}, "insuranceOfVehicleInTotalFromBreakage": {"selected": true, "liabilityId": 1222993111957505}}}, "gapForLeasing": {"selected": false, "productId": 1693718373515264, "productCode": "1699000040", "liabilities": {"gapForLeasing": {"selected": true, "liabilityId": 1693745351278592}}}, "glassBreakage": {"selected": false, "productId": 1254853850447873, "productCode": "039946", "liabilities": {"glassBreakageFixedCompensation1000Euro": {"selected": true, "liabilityId": 1227194462388226}, "glassBreakageFixedCompensation2000Euro": {"selected": false, "liabilityId": 1227195016036355}}}, "animalCollision": {"selected": false, "productId": 1254977934737411, "productCode": "039947", "liabilities": {"animalCollision": {"selected": true, "liabilityId": 1227196978970628}}}}, "vehicleInfo": {"vehicleGroup": "Passenger", "isNewVehicle": true, "vehicleInAdvertisingAndBranding": false, "E_VATRate": "0.2", "E_PPMV": "0", "plateType": "1", "vehicleLeasing": false, "isNotRegistered": false, "registrationArea": "Serbia", "claimExperience": {"claimNumberInLast5Year": 0, "claimNumberInLast3Year": 0, "claimNumberLastYear": 0, "lossRatioLastYear": "0"}, "carOwner": {"customerType": "INDIVIDUAL"}}}