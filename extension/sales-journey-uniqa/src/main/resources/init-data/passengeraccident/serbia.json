{"paymentMethod": "BANK_SLIP", "issueWithoutPayment": false, "displayCurrency": "EUR", "E_IsDiplomat": false, "relationPolicyTemplateCode": "MTPL_Purchase_Rider_After_Main_Product", "passengerAccidentGoods": {"goodsCode": "PassengerAccidentInsurance", "goodsId": ****************, "selected": true, "isRenewalPolicy": false, "plan": {"planId": ****************, "premiumFrequencyType": "SINGLE", "premiumPeriodType": "YEARFULL", "premiumPeriod": "1", "coveragePeriodType": "DAY", "coveragePeriod": "365", "saCurrency": "EUR", "baseCurrency": "RSD", "premiumCurrency": "RSD", "elements": {"nonStandardTariff": false}}, "elements": {"E_Exposure": "0", "E_Frequency": "0"}}, "products": {"passengerAccidentMainProduct": {"mainProduct": true, "selected": true, "productCode": "010801", "productId": ****************, "liabilities": {"deathDueToAccident": {"selected": true, "liabilityId": 530}, "disability": {"selected": true, "liabilityId": 698}, "medicalCostAndLoss": {"selected": true, "liabilityId": ****************}}}}, "vehicleInfo": {"vehicleGroup": "Passenger", "isNewVehicle": true, "vehicleInAdvertisingAndBranding": false, "E_VATRate": "0.2", "E_PPMV": "0", "plateType": "1", "vehicleLeasing": false, "isNotRegistered": false, "registrationArea": "Serbia", "machineUsageType": "Unknown", "claimExperience": {"claimNumberInLast5Year": 0, "claimNumberInLast3Year": 0, "claimNumberLastYear": 0, "lossRatioLastYear": "0"}, "carOwner": {"customerType": "INDIVIDUAL"}}}