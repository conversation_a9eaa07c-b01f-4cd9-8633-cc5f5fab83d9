/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa;

import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.global.IErrorCodeRegistry;
import com.zatech.genesis.sales.journey.client.biz.common.processor.payment.errorcdoes.PaymentHandleErrorCodes;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.QuotationErrorCode;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.partyinfo.PartyInfoErrorCode;
import com.zatech.genesis.sales.journey.uniqa.datavalidate.coveragecheck.CdcServiceErrorCode;
import com.zatech.genesis.sales.journey.uniqa.datavalidate.coveragecheck.CoverageCheckErrorCode;
import com.zatech.genesis.sales.journey.uniqa.errorcode.CommonErrorCode;
import com.zatech.genesis.sales.journey.uniqa.errorcode.FileProcessorErrorCode;
import com.zatech.genesis.sales.journey.uniqa.errorcode.PersonInfoCheckErrorCode;

import java.util.Set;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/5/22 09:50
 **/
@Component
public class UniqaErrorCodeRegistry implements IErrorCodeRegistry {

    @Override
    public Set<Class<? extends IErrorCode>> registeredErrorCodes() {
        return Set.of(PaymentHandleErrorCodes.class, UniqaErrorCodes.class, QuotationErrorCode.class,
                PartyInfoErrorCode.class, CdcServiceErrorCode.class, CoverageCheckErrorCode.class,
                CommonErrorCode.class, FileProcessorErrorCode.class, PersonInfoCheckErrorCode.class);
    }
}
