/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service;

import com.zatech.gaia.resource.components.enums.policy.PolicyStatusEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPlan;
import com.zatech.genesis.sales.journey.uniqa.errorcode.PolicyErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.context.ConvertPolicyResponseContext;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import static com.za.cqrs.util.Functions.doIf;
import static java.util.Optional.ofNullable;

/**
 * @Author: weizhen.kong
 */
@Component
public class PaStandAlonePolicyConvert extends AbstractPolicyConvert {

    @Override
    public boolean support(ScenarioTypeEnum scenario) {
        return ScenarioTypeEnum.passengerAccident.equals(scenario);
    }

    @Override
    public BasicUiModel convert(IssuanceRequest request, BasicUiModel model, ConvertPolicyResponseContext context, PolicyResponse policyResponse) {
        // PaStandAlone 对于满期失效或者保单终止不能购买PA
        if (PolicyStatusEnum.LAPSED.equals(policyResponse.getPolicyStatus()) || PolicyStatusEnum.TERMINATION.equals(policyResponse.getPolicyStatus())) {
            throw CommonException.byError(PolicyErrorCode.POLICY_LAPSED_OR_TERMINATION);
        }
        doIf(isMtplGoods(policyResponse.getGoodsCode(), policyResponse.getGoodsId()), () -> {
            AutoGoods autoGoods = new AutoGoods();
            AutoPlan autoPlan = new AutoPlan();
            autoPlan.setPlanId(policyResponse.getGoodsPlanId());
            autoGoods.setPlan(autoPlan);
            autoGoods.setGoodsId(policyResponse.getGoodsId());
            autoGoods.setPlan(autoPlan);
            model.setTrafficCompulsoryInsuranceGoods(autoGoods);
        });
        doIf(CollectionUtils.isNotEmpty(request.getIssuanceProductList()), () ->
                request.getIssuanceProductList().forEach(issuanceProduct ->
                        policyResponse.getPolicyProductList().stream()
                                .filter(policyProduct -> policyProduct.getProductId().equals(issuanceProduct.getProductId()))
                                .findFirst()
                                .ifPresent(policyProduct -> {
                                    issuanceProduct.setEffectiveDate(policyProduct.getEffectiveDate());
                                    issuanceProduct.setExpiryDate(policyProduct.getExpiryDate());
                                })));
        BasicUiModel uiModel = super.convert(request, model, context, policyResponse);
        ofNullable(model.getVehicleInfo()).ifPresent(vehicleInfo -> vehicleInfo.setRegistrationArea(null));
        return uiModel;
    }

}
