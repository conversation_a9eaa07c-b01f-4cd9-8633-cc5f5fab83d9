/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result;

import com.zatech.gaia.resource.components.enums.product.AdditionalTypeEnum;
import com.zatech.gaia.resource.components.enums.product.LoadingMethodEnum;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class LiabilityExtraPremiumOutput {

    /**
     * 加费类型 {@link AdditionalTypeEnum}
     */
    private AdditionalTypeEnum loadingType;

    /**
     * 加费方式  {@link LoadingMethodEnum}
     */
    private LoadingMethodEnum loadingMethod;

    /**
     * 顺序
     */
    private Integer orderNo;

    /**
     * 折扣比例
     */
    private String rate;

    /**
     * 加费(是否含税取决于产品上的含税配置)
     */
    private String periodExtraPremium;

    /**
     * periodExtraPremiumBeforeCap
     */
    private String periodExtraPremiumBeforeCap;

    /**
     * periodExtraPremiumCapDelta
     */
    private String periodExtraPremiumCapDelta;

    /**
     * 年化加费(是否含税取决于产品上的含税配置)
     */
    private String annualExtraPremium;

    /**
     * 加费后的金额
     */
    private String periodPremiumAfterCurrentCalculation;

    private String coverageTotalExtraPremium;

    private String coverageTotalPremiumAfterCurrentCalculation;

}
