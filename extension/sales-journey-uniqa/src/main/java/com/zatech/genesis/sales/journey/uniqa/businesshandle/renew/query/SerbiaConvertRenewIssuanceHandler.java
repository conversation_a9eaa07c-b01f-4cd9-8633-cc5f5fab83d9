/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.RenewPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.result.ConvertRenewIssuanceResult;
import com.zatech.genesis.sales.journey.uniqa.uimodel.serbia.SerbiaUiModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@UIModelBusinessHandler(name = "renew", kind = BusinessHandlerKind.renew, desc = "Convert issuance business handler", tags = Consts.SERBIA)
public class SerbiaConvertRenewIssuanceHandler implements IAuthBusinessHandler<RenewPhase, SerbiaUiModel, ConvertRenewIssuanceResult> {

    @Autowired
    private ConvertRenewIssuanceHandler handler;

    @Override
    public RenewPhase[] supportedPhases() {
        return new RenewPhase[]{RenewPhase.previousPolicyQuery};
    }

    @Override
    public Result onStop(RenewPhase phase, SerbiaUiModel param, ConvertRenewIssuanceResult businessModel, BusinessHandleContext context) {
        return handler.onStop(phase, param, businessModel, context);
    }

    @Override
    public FlowStrategy handle(RenewPhase phase, SerbiaUiModel param, ConvertRenewIssuanceResult businessModel, BusinessHandleContext context) {
        return handler.handle(phase, param, businessModel, context);
    }
}
