/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.google.common.collect.Lists;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceCancelBatchRequest;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.UIModelContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderDeleteService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderQueryService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderUpdateService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.UpdateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.issuance.IssuanceService;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.order.api.PolicyRecordManager;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class CancelInsuranceService {

    @Autowired
    private IssuanceService issuanceService;

    @Autowired
    private PolicyRecordManager policyRecordManager;

    @Autowired
    private IOuterPolicyService iOuterPolicyService;

    @Autowired
    private UIModelOrderDeleteService orderDeleteService;

    @Autowired
    private UIModelOrderQueryService orderQueryService;

    @Autowired
    private UIModelOrderUpdateService orderUpdateService;

    /**
     * sub orderId
     */
    @Transactional
    public IssuanceResponse cancel(Long orderId, String remark) {
        IssuanceCancelBatchRequest request = new IssuanceCancelBatchRequest();
        PolicyRecordInfoOutput policyRecord = policyRecordManager.query(orderId);
        if (policyRecord == null) {
            log.info("CancelInsuranceService.cancel orderId:{} policyRecord is null", orderId);
            return null;
        }
        IssuanceResponse issuance = iOuterPolicyService.getIssuance(policyRecord.getIssuanceNo(), YesNoEnum.YES);

        if (issuance == null || issuance.getIssuanceStatus() != IssuanceStatusEnum.WITHDRAWN) {
            request.setReason(remark);
            if (policyRecord.isTempInsurance()) {
                request.setTemporaryIssuanceNoList(Lists.newArrayList(policyRecord.getIssuanceNo()));
            } else {
                request.setIssuanceNoList(Lists.newArrayList(policyRecord.getIssuanceNo()));
            }
            issuanceService.batchCancel(request);
        } else {
            log.info("Issuance({}) is withdrawn, request:{}", policyRecord.getIssuanceNo(), JsonParser.toJsonString(request));
        }
        policyRecordManager.deleteByOrderId(orderId);

        return iOuterPolicyService.getIssuance(policyRecord.getIssuanceNo(), YesNoEnum.YES);
    }

    @Transactional
    public void deleteOrder(Long orderId, String remark) {

        UIModelOrderOutput<IUIModel> orderOutput = orderQueryService.queryOrThrow(orderId);

        if (orderOutput.isTransactionOrder()) {
            ContextHolder contextHolder = new ContextHolder(orderOutput.getTransactionOrderContextOpt().orElse(orderOutput));
            contextHolder.getOrderContextHolders()
                .forEach(e -> {
                    this.cancel(e.getOrderId(), remark);
                    orderDeleteService.deleteSubOrder(orderOutput.getTransactionId(), orderId);
                });
            orderDeleteService.deleteTransaction(orderOutput.getTransactionId());
            return;
        }

        IssuanceResponse issuanceResponse = this.cancel(orderOutput.getOrderId(), remark);
        orderDeleteService.deleteSubOrder(orderOutput.getTransactionId(), orderId);
        orderOutput.getTransactionOrderContextOpt().ifPresent(transactionUiOrder -> updateUiModelForSelectedGoods(transactionUiOrder, issuanceResponse.getGoodsId()));
    }

    private void updateUiModelForSelectedGoods(OrderContext orderOutput, Long goodsId) {
        if (orderOutput.getUiModelOpt().isEmpty()) {
            log.warn("CancelInsuranceService.updateUiModelForSelectedGoods orderNo:{} , uiModels don't exist", orderOutput.getOrderNo());
            return;
        }

        BasicUiModel basicUiModel = (BasicUiModel) orderOutput.getUiModelOpt().flatMap(UIModelContext::getDataOpt).orElse(null);
        if (basicUiModel == null) {
            log.warn("CancelInsuranceService.updateUiModelForSelectedGoods orderNo:{} , transactionOrderUiModel don't exist", orderOutput.getOrderNo());
            return;
        }
        basicUiModel.getAllGoods().stream()
            .filter(e -> Objects.equals(e.getGoodsId(), goodsId))
            .filter(e -> Boolean.TRUE.equals(e.getSelected()))
            .findFirst()
            .ifPresent(e -> {
                e.setSelected(false);
                log.info("Update the goods[{}] of the UiModel of order[{}] to be false", e.getGoodsCode(), orderOutput.getOrderNo());
                orderUpdateService.updateOrThrow(orderOutput.getOrderId(), null, new UpdateUIModelInput<>(basicUiModel, null, true));
            });

    }

}
