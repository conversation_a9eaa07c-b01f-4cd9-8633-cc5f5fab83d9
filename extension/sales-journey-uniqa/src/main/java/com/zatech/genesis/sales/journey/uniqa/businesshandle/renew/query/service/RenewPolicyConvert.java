/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service;

import com.google.common.collect.Lists;
import com.zatech.genesis.policy.api.base.PolicyProductBase;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPolicyElements;
import com.zatech.genesis.sales.journey.integration.market.MarketAdapter;
import com.zatech.genesis.sales.journey.integration.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.BooleanToYesNoEnum;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.context.ConvertPolicyResponseContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;

import java.util.List;
import java.util.Objects;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

import static com.za.cqrs.util.Functions.doIf;
import static java.util.Optional.ofNullable;

/**
 * @Author: weizhen.kong
 */
@Component
@AllArgsConstructor
public class RenewPolicyConvert extends AbstractPolicyConvert {

    private final MarketAdapter marketAdapter;

    @Override
    public boolean support(ScenarioTypeEnum scenario) {
        return ScenarioTypeEnum.renew.equals(scenario);
    }

    @Override
    public BasicUiModel convert(IssuanceRequest request, BasicUiModel model, ConvertPolicyResponseContext context, PolicyResponse policyResponse) {
        BasicUiModel basicUiModel = super.convert(request, model, context, policyResponse);
        List<Long> retainFieldsOfGoodsIds = Lists.newArrayList(policyResponse.getGoodsId());
        doIf(isMtplGoods(policyResponse.getGoodsCode(), policyResponse.getGoodsId()), () ->
                basicUiModel.queryGoodsId(GoodsCodeEnum.MTPL_Riders)
                        .ifPresentOrElse(retainFieldsOfGoodsIds::add,
                                () -> marketAdapter.queryGoodsBasicOpt(GoodsCodeEnum.MTPL_Riders.getGoodsCode()).map(GoodsBasicInfoResp::getGoodsId).ifPresent(retainFieldsOfGoodsIds::add)));
        basicUiModel.retainFieldsBy(retainFieldsOfGoodsIds);
        ofNullable(basicUiModel.selectedGoods(policyResponse.getGoodsId())).ifPresent(goods -> {
            doIf(Objects.isNull(goods.getElements()), () -> goods.setElements(new AutoPolicyElements()));
            goods.getElements().setOldPolicyExpireDate(policyResponse.getPolicyProductList().stream().filter(policyProduct -> Objects.isNull(policyProduct.getMainId())).map(PolicyProductBase::getExpiryDate).findFirst().orElse(null));
            goods.getElements().setPreviousMTPLPolicySerialNumber(request.getPolicySerialNumber());
            goods.getElements().setSerialNumber(null);
        });
        //ui model最外层的数据convert显示处理
        model.setIssueWithoutPayment(BooleanToYesNoEnum.convert(request.getIssueWithoutPayment()));
        return basicUiModel;
    }

}