/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.IssuanceCreateResponse;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.authenticate.UserInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderCreateService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderDeleteService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderQueryService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.issuance.IssuanceService;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.order.api.PolicyRecordManager;
import com.zatech.genesis.sales.journey.order.api.input.CreatePolicyRecordInput;
import com.zatech.genesis.sales.journey.order.api.input.UpdatePolicyRecordInput;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.IssuanceCheckService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE;
import static com.zatech.genesis.sales.journey.client.biz.common.util.IssuanceUtils.createGoodsId;
import static com.zatech.genesis.sales.journey.client.biz.common.util.IssuanceUtils.updateGoodsId;

@Slf4j
@Component
public class WaitingInsuranceService {

    @Autowired
    private UIModelServiceFactory uiModelServiceFactory;

    @Autowired
    private UIModelOrderDeleteService uiModelOrderDeleteService;

    @Autowired
    private UIModelOrderCreateService uiModelOrderCreateService;

    @Autowired
    private UIModelOrderQueryService uiModelOrderQueryService;

    @Autowired
    private IssuanceService issuanceService;

    @Autowired
    private CancelInsuranceService cancelInsuranceService;

    @Autowired
    private Converters converters;

    @Autowired
    private PolicyRecordManager policyRecordManager;

    @Autowired
    private IssuanceCheckService issuanceCheckService;

    /**
     * main or sub orderId
     */
    @Transactional
    public IssuanceResult handle(BusinessHandleContext context) {
        Long orderId = context.getOrder().getOrderId();
        log.info("Update waiting insurance handle orderId: {}", orderId);
        UIModelOrderOutput<BasicUiModel> query = uiModelServiceFactory.getQueryService().query(orderId);
        ContextHolder contextHolder = new ContextHolder(query, context.getUserInfo().orElse(null));

        issuanceCheckService.checkIfLockByUser(contextHolder);

        Long mainOrderId = contextHolder.getTransactionOrderContext().map(OrderContextHolder::getOrderContext).map(OrderContext::getOrderId).orElse(null);
        List<Long> selectedGoodsId = query.getDataOpt().map(BasicUiModel::selectedGoodsId).orElse(Collections.emptyList());
        List<Long> goodsIdList = persistenceGoodsId(contextHolder);

        deleteRecord(contextHolder, selectedGoodsId, mainOrderId);
        List<IssuanceResponse> updateResultList = updateRecord(contextHolder, updateGoodsId(selectedGoodsId, goodsIdList), query);
        List<IssuanceCreateResponse> createResponseList = createRecord(contextHolder, mainOrderId, createGoodsId(selectedGoodsId, goodsIdList), query);

        return IssuanceResult.builder()
            .changeResponses(updateResultList)
            .createResponses(createResponseList)
            .build();
    }

    private List<IssuanceCreateResponse> createRecord(ContextHolder contextHolder, Long mainOrderId, List<Long> goodsIdList, UIModelOrderOutput<BasicUiModel> query) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return Lists.newArrayList();
        }
        List<IssuanceRequest> createRequestList = goodsIdList
            .stream()
            .map(e -> {
                UIModelOrderOutput<IUIModel> newOrder = uiModelOrderCreateService.createSubOrder(mainOrderId, null, null);
                AutoConvertIssuanceRequestContext context = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(newOrder, TraceOp.getTenant()));
                CreatePolicyRecordInput policyRecord = new CreatePolicyRecordInput();
                policyRecord.setIssuanceTransactionType(WAITING_FOR_CREATEISSUANCE);
                policyRecord.setGoodsId(e);
                policyRecord.setOrderId(newOrder.getOrderId());
                policyRecordManager.create(policyRecord);

                context.setSelectedGoodsId(e);
                context.setPhase(PolicyPhase.waitingInsurance);
                BusinessHandleContext businessHandleContext = context.getBusinessHandleContext();
                contextHolder.getUserInfoHolder().ifPresent(x -> businessHandleContext.setUserInfo(x.getUserInfo()));

                return query.getDataOpt().map(x -> {
                    context.setSelectedGoods(x.selectedGoods(e));
                    IssuanceRequest issuanceRequest = new IssuanceRequest();
                    issuanceRequest = converters.convert(x, issuanceRequest, context);
                    issuanceRequest.setIssuanceTransactionType(WAITING_FOR_CREATEISSUANCE);
                    return issuanceRequest;
                }).orElse(null);
            }).filter(Objects::nonNull).collect(Collectors.toList());

        IssuanceResult createResultList = issuanceService.batchCreate(createRequestList, null);

        createResultList.getCreateResponses().forEach(e -> {
            UpdatePolicyRecordInput policyRecord = new UpdatePolicyRecordInput();
            policyRecord.setPolicyNo(e.getPolicyNo());
            policyRecord.setIssuanceNo(e.getIssuanceNo());
            policyRecord.setOrderId(uiModelOrderQueryService.query(e.getBizApplyNo()).getOrderId());
            policyRecordManager.update(policyRecord);
        });

        return createResultList.getCreateResponses();
    }

    private List<IssuanceResponse> updateRecord(ContextHolder contextHolder, List<Long> goodsIdList, UIModelOrderOutput<BasicUiModel> query) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return Lists.newArrayList();
        }
        BasicUiModel basicUiModel = query.getDataOpt().orElse(null);
        List<IssuanceRequest> updateRequestList = contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(e -> goodsIdList.contains(e.getGoodsId()))
            .map(e -> {
                OrderContext orderContext = contextHolder.queryByOrderId(e.getOrderId()).getOrderContext();
                AutoConvertIssuanceRequestContext issuanceRequestContext = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(orderContext, TraceOp.getTenant()));
                BusinessHandleContext businessHandleContext = issuanceRequestContext.getBusinessHandleContext();
                contextHolder.getUserInfoHolder().ifPresent(x -> businessHandleContext.setUserInfo(x.getUserInfo()));
                issuanceRequestContext.setSelectedGoodsId(e.getGoodsId());
                Optional.ofNullable(basicUiModel).ifPresent(b -> issuanceRequestContext.setSelectedGoods(b.selectedGoods(e.getGoodsId())));
                issuanceRequestContext.setPhase(PolicyPhase.waitingInsurance);
                IssuanceRequest issuanceRequest = new IssuanceRequest();
                issuanceRequest = converters.convert(query.getDataOpt().get(), issuanceRequest, issuanceRequestContext);
                issuanceRequest.setIssuanceTransactionType(WAITING_FOR_CREATEISSUANCE);
                issuanceRequest.setIssuanceNo(e.getIssuanceNo());
                return issuanceRequest;
            })
            .collect(Collectors.toList());

        updateRequestList.stream().map(IssuanceRequest::getIssuanceNo).filter(Objects::nonNull).forEach(issuanceCheckService::checkStatus);
        return updateWaitingInsurance(updateRequestList);
    }

    private void deleteRecord(ContextHolder contextHolder, List<Long> goodsIdList, Long mainOrderId) {
        if (CollectionUtils.isEmpty(goodsIdList)) {
            return;
        }
        //需要删除的orderIds
        List<Long> deletedOrderId = contextHolder.getOrderContextHolders()
            .stream()
            .filter(e -> e.getPolicyRecord().map(p -> !goodsIdList.contains(p.getGoodsId())).orElse(true))
            .map(OrderContextHolder::getOrderId).toList();

        contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(PolicyRecordInfoOutput::isTempInsurance)
            .filter(e -> !goodsIdList.contains(e.getGoodsId()))
            .map(PolicyRecordInfoOutput::getOrderId)
            .forEach(cancelOrderId -> cancelInsuranceService.cancel(cancelOrderId, null));

        deletedOrderId.forEach(deleteOrderId ->  {
            uiModelOrderQueryService.queryOpt(deleteOrderId).ifPresent(e -> uiModelOrderDeleteService.deleteSubOrder(mainOrderId, deleteOrderId));
        });
    }

    protected List<IssuanceResponse> updateWaitingInsurance(List<IssuanceRequest> updateRequestList) {
        return issuanceService.batchUpdate(updateRequestList);
    }

    private List<Long> persistenceGoodsId(ContextHolder contextHolder) {
        return contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(PolicyRecordInfoOutput::getGoodsId)
            .collect(Collectors.toList());
    }

}
