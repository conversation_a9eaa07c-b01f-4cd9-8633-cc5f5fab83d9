/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.underwriting;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.UnderWritingBusinessResult;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service.UnderwritingService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class BaseUwBusinessHandler {

    @Autowired
    private UnderwritingService underwritingService;

    @Transactional
    public Result onStop(UnderWritingBusinessResult result) {
        return underwritingService.onStop(result);
    }

    @Transactional
    public void handle(PolicyPhase phase, BasicUiModel uiModel, UnderWritingBusinessResult businessModel, BusinessHandleContext context) {
        BeanUtils.copyProperties(underwritingService.handle(phase, uiModel, context), businessModel);
    }

}