/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.campaign;

import com.google.common.collect.Lists;
import com.za.taylor.core.TaylorProxy;
import com.za.taylor.enums.DecisionType;
import com.za.taylor.enums.RuleActionType;
import com.za.taylor.model.RuleResult;
import com.za.taylor.model.request.FetchRuleDataRequest;
import com.zatech.gaia.resource.components.enums.schema.SaPremiumCalculationBizTypeEnum;
import com.zatech.gaia.resource.graphene.market.campaign.CampaignCategoryEnum;
import com.zatech.gaia.resource.graphene.market.campaign.CampaignDiscountValueTypeEnum;
import com.zatech.gaia.resource.graphene.market.campaign.CampaignRateSourceEnum;
import com.zatech.genesis.campaign.api.structure.base.CampaignBaseResponse;
import com.zatech.genesis.campaign.api.structure.base.CampaignBasicInfoBase;
import com.zatech.genesis.campaign.api.structure.request.QueryCampaignRequest;
import com.zatech.genesis.campaign.api.structure.response.CampaignBasicInfoResponse;
import com.zatech.genesis.campaign.api.structure.response.CampaignParticipationCriteriaResponse;
import com.zatech.genesis.campaign.api.structure.response.CampaignPartnerManagementResponse;
import com.zatech.genesis.campaign.api.structure.response.CampaignPremiumDiscountPayerResponse;
import com.zatech.genesis.campaign.api.structure.response.CampaignPremiumDiscountPeriodResponse;
import com.zatech.genesis.campaign.api.structure.response.CampaignResponse;
import com.zatech.genesis.campaign.api.structure.response.category.CampaignCatePremiumDiscountResponse;
import com.zatech.genesis.market.api.calculate.request.campaign.CampaignPayerRequest;
import com.zatech.genesis.market.api.calculate.request.sapremium.CampaignRequest;
import com.zatech.genesis.market.api.calculate.request.sapremium.PromotionRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.dataprovide.feeadjustmentconfig.service.config.BizDateService;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.CampaignItem;
import com.zatech.genesis.sales.journey.client.biz.common.model.rule.RuleCheckContext;
import com.zatech.genesis.sales.journey.client.biz.common.model.rule.RuleFactorModel;
import com.zatech.genesis.sales.journey.integration.campaign.outer.OuterCampaignService;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.QuotationErrorCode;
import com.zatech.genesis.sales.journey.uniqa.datavalidate.rulecheck.AbstractRuleGroupCheckDataValidator;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.common.CommonConvertContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.rulecheck.UiModelToRuleConverterBuilder;
import com.zatech.octopus.framework.bizdate.BusinessDateService;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.function.BooleanSupplier;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.mapBy;
import static com.za.cqrs.util.Functions.throwIf;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

/**
 * goods + plan:
 * <ul>
 *     <li> campA: rate </li>
 *     <li> campB: promotion </li>
 *     <li> campC: promotion + rate </li>
 * <ul/>
 */
@Slf4j
@Component
public class SinglePromotionCodeLogic extends AbstractRuleGroupCheckDataValidator implements CampaignParsingLogic {

    @Resource
    private OuterCampaignService campaignService;

    @Resource
    private TaylorProxy taylorProxy;

    @Resource
    private BusinessDateService dateService;

    @Resource
    private BizDateService bizDateService;

    @Resource
    private UiModelToRuleConverterBuilder uiModelToRuleConverterBuilder;

    private static final Integer DEFAULT_CAMPAIGN_PARTICIPATION_PERIOD_NO = 1;

    @Override
    public List<CampaignRequest> assemblyCampaignRequest(BasicUiModel model, CommonConvertContext context, AutoGoods goods, IPhase marketPhase) {
        return assemblyCampaignRequest(goods.getGoodsId(), goods.getPlan().getPlanId(), context, model, goods, marketPhase);
    }

    //外部没有传入promotionCode
    private List<CampaignRequest> assemblyCampaignRequest(Long goodsId, Long planId, CommonConvertContext context, BasicUiModel basicUiModel, AutoGoods goods, IPhase marketPhase) {
        val campaignRequests = new ArrayList<CampaignRequest>();
        val campaignResponse = new ArrayList<CampaignResponse>();
        //这个时候看一下外部是否传入了campaign
        val campaignByPromotionCode = ofNullable(goods.getCampaigns()).orElseGet(Collections::emptyList).stream()
            .filter(campaignInput -> Objects.nonNull(campaignInput.getPromotionCode()))
            .collect(Collectors.toList());
        /* 1. promotion*/
        //如果在campaign层级传入了promotionCode
        if (CollectionUtils.isNotEmpty(campaignByPromotionCode)) {
            campaignByPromotionCode.forEach(campaignInput -> {
                val promotionCode = campaignInput.getPromotionCode();
                //根据promotionCode查询匹配campaign，匹配不上会抛出异常
                val response = getCampaignResponseByPromotionCode(goodsId, planId, promotionCode, basicUiModel, goods).stream()
                    .filter(x -> Objects.isNull(campaignInput.getCampaignCode()) || Objects.equals(x.getBasicInfo().getCampaignCode(), campaignInput.getCampaignCode()))
                    .filter(x -> filterByConditionRule(x, basicUiModel, goods, context, null, marketPhase))
                    .toList();
                campaignResponse.addAll(response);
                val campaignInfos = getCampaignInfosByAgent(response, context.getOrderContext().getChannelCode());
                campaignRequests.addAll(builderCampaignRequestListByPromotion(promotionCode, campaignInfos));
            });
        }

        //所有的campaign，过滤不满足rule的
        /* 2. all campaign*/
        val campaignResponsesList = mergeCampaignResponses(goods, goodsId, planId, basicUiModel, context.getOrderContext().getChannelCode(), campaignResponse, campaignResponse.stream().map(x -> x.getBasicInfo().getCampaignCode()).collect(Collectors.toList()))
            .stream()
            .filter(x -> filterByConditionRule(x, basicUiModel, goods, context, null, marketPhase))
            .collect(Collectors.toList());
        campaignRequests.removeIf(ca -> campaignResponsesList.stream().noneMatch(cr -> Objects.equals(ca.getCampaignCode(), cr.getBasicInfo().getCampaignCode())));
        //   discount category campaign by [rule , user input , fixed]
        val mergeCampaignRequests = builderCampaignRequestListByRuleOrUserInput(goods, context.getOrderContext(), campaignResponsesList, basicUiModel, marketPhase);
        //  others category campaign
        mergeCampaignRequests.addAll(getOthersCategoryCampaign(campaignResponsesList));

        // exclusion (dont A exclusion B, B exclusion A => remove A,B)
        var campaignCodes = Optional.of(mergeCampaignRequests).orElseGet(Lists::newArrayList).stream()
            .collect(Collectors.toMap(CampaignRequest::getCampaignCode, Function.identity()));
        var filterCampaigns = campaignResponsesList.stream()
            .filter(cr -> campaignCodes.containsKey(cr.getBasicInfo().getCampaignCode()))
            .toList();
        //TODO rule convert need rewrite

        var filterCampaignResponses = campaignResponsesList.stream()
            .filter(x -> filterByConditionRule(x, basicUiModel, goods, context, filterCampaigns, marketPhase))
            .map(CampaignBaseResponse::getBasicInfo)
            .collect(Collectors.toMap(CampaignBasicInfoBase::getCampaignCode, Function.identity()));
        var filterCampaignRequests = mergeCampaignRequests.stream()
            .filter(x -> filterCampaignResponses.containsKey(x.getCampaignCode())).toList();

        val campaignRequestsToMap = campaignRequests.stream()
            .collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(CampaignRequest::getCampaignCode))), ArrayList::new)).stream()
            .collect(Collectors.toMap(CampaignRequest::getCampaignCode, Function.identity()));

        /* 3.merge*/
        var repeatedCampaignRequest = new ArrayList<CampaignRequest>();
        var mrCampaignRequest = filterCampaignRequests.stream().map(e -> {
            if (campaignRequestsToMap.containsKey(e.getCampaignCode())) {
                CampaignRequest request = campaignRequestsToMap.get(e.getCampaignCode());
                e.setCampaignParticipationPeriodNo(request.getCampaignParticipationPeriodNo());
                e.setNewInsuredDimensions(request.getNewInsuredDimensions());
                e.setPromotions(request.getPromotions());
                repeatedCampaignRequest.add(request);
            }
            return e;
        }).collect(Collectors.toList());

        campaignRequests.removeAll(repeatedCampaignRequest);
        mrCampaignRequest.addAll(campaignRequests);

        return mrCampaignRequest.stream()
            .collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparing(CampaignRequest::getCampaignCode))), ArrayList::new));
    }

    private List<CampaignResponse> mergeCampaignResponses(AutoGoods goods, Long goodsId, Long planId, BasicUiModel basicUiModel, String channelCode, List<CampaignResponse> campaignResponse, List<String> campaignByPromotionCode) {
        //获取当前goods和plan所关联的所有campaign
        var responseByGoods = getCampaignResponse(goods, basicUiModel, goodsId, planId);
        //把上边根据promotionCode中匹配到的campaign放到产品关联的campaign中，（不会出现重复吗？）
        responseByGoods.addAll(campaignResponse);
        //拿到merge以后的campaign，然后过滤支持当前渠道的，过滤CampaignInventoryUseMethod为空的，或者
        List<CampaignResponse> campaignResponses = responseByGoods.stream()
            .filter(camp -> CollectionUtils.isNotEmpty(camp.getPartnerManagements())
                && camp.getPartnerManagements().stream()
                .map(CampaignPartnerManagementResponse::getSalesChannelCode)
                .anyMatch(campaignChannelCode -> Objects.equals(campaignChannelCode, channelCode)))
            .filter(camp -> !isPromotionCode(camp.getParticipationCriteria()) || campaignByPromotionCode.contains(camp.getBasicInfo().getCampaignCode()))
            .collect(collectingAndThen(toCollection(() -> new TreeSet<>(Comparator.comparingLong(value -> value.getBasicInfo().getCampaignId()))), ArrayList::new));
        return campaignResponses;
    }

    private boolean isPromotionCode(CampaignParticipationCriteriaResponse criteria) {
        if (criteria == null) {
            return false;
        }
        return criteria.getCampaignInventoryUseMethod() != null;
    }

    private List<CampaignRequest> getOthersCategoryCampaign(List<CampaignResponse> campaignResponses) {
        return campaignResponses.stream()
            .filter(e -> CampaignCategoryEnum.PREMIUM_DISCOUNT != e.getBasicInfo().getCategory())
            .map(e -> {
                val request = new CampaignRequest();
                request.setCampaignCode(e.getBasicInfo().getCampaignCode());
                return request;
            }).collect(Collectors.toList());
    }

    private List<CampaignRequest> builderCampaignRequestListByRuleOrUserInput(AutoGoods goods, OrderContext order, List<CampaignResponse> campaignResponse, BasicUiModel basicUiModel, IPhase marketPhase) {
        var requests = new ArrayList<CampaignRequest>();
        val parent = ofNullable(campaignResponse).orElseGet(Collections::emptyList).stream()
            .filter(e -> CampaignCategoryEnum.PREMIUM_DISCOUNT == e.getBasicInfo().getCategory())
            .filter(e -> ofNullable(e.getCatePremiumDiscount())
                .map(x -> CollectionUtils.isNotEmpty(x.getPeriodList()))
                .orElse(false)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parent)) {
            return requests;
        }

        val userInput = parent.stream().filter(e -> e.getCatePremiumDiscount().getPeriodList().stream()
                .flatMap(p -> p.getPayerList().stream())
                .anyMatch(c -> Objects.equals(CampaignDiscountValueTypeEnum.USER_INPUT.getCode(), c.getDiscountValueType())))
            .collect(Collectors.toList());
        //  1. user input
        addCampaignRequestByUserInput(requests, userInput, goods.getCampaigns());

        val campaignCodeByUserInput = userInput.stream()
            .filter(e -> Objects.nonNull(e.getBasicInfo()))
            .map(e -> e.getBasicInfo().getCampaignCode()).collect(Collectors.toList());

        //  2. rule
        addCampaignRequestByRule(goods, order, basicUiModel, requests, parent, campaignCodeByUserInput, marketPhase);

        //  3.  fixed & formula
        addCampaignRequestByFixed(parent, requests, campaignCodeByUserInput, goods.getCampaigns());
        return requests;
    }

    private void addCampaignRequestByFixed(List<CampaignResponse> parent, ArrayList<CampaignRequest> requests, List<String> campaignCodeByUserInput, List<CampaignItem> campaignInputs) {
        val campaignRequestByFixed = parent.stream()
            .filter(e -> Objects.nonNull(e.getBasicInfo()) && !campaignCodeByUserInput.contains(e.getBasicInfo().getCampaignCode()))
            .filter(e -> e.getCatePremiumDiscount().getPeriodList().stream()
                .flatMap(p -> p.getPayerList().stream())
                .anyMatch(c -> Objects.equals(CampaignDiscountValueTypeEnum.PRE_DEFINED.getCode(), c.getDiscountValueType()) || Objects.equals(CampaignDiscountValueTypeEnum.DISCOUNT_BY_FORMULA.getCode(), c.getDiscountValueType())))
            .map(response -> {
                val payers = response.getCatePremiumDiscount().getPeriodList().stream()
                    .filter(p -> CollectionUtils.isNotEmpty(p.getPayerList()))
                    .flatMap(p -> p.getPayerList().stream())
                    .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(payer -> "" + payer.getPayerType() + "-" + payer.getPayerCode()))), ArrayList::new));
                val campaignToMaps = Optional.ofNullable(campaignInputs).orElseGet(Lists::newArrayList).stream()
                    .collect(Collectors.toMap(CampaignItem::getCampaignCode, Function.identity()));
                val isEnumeration = payers.stream().anyMatch(payer -> Objects.equals(payer.getRateSource(), CampaignRateSourceEnum.ENUMERATION_RATE.getCode()));
                if (isEnumeration) {
                    if (campaignToMaps.containsKey(response.getBasicInfo().getCampaignCode())) {
                        //  TODO Only one payer can be configured
                        val input = campaignToMaps.get(response.getBasicInfo().getCampaignCode());
                        val payerRequests = mapBy(payers, payer -> {
                            var payerRequest = new CampaignPayerRequest();
                            payerRequest.setPayerCode(payer.getPayerCode());
                            payerRequest.setPayerSubCode(payer.getPayerSubCode());
                            payerRequest.setPayerType(payer.getPayerType());
                            if (Objects.equals(payer.getRateSource(), CampaignRateSourceEnum.ENUMERATION_RATE.getCode())) {
                                payerRequest.setDiscountRate(input.getRate().toPlainString());
                            }
                            return payerRequest;
                        });
                        var request = new CampaignRequest();
                        request.setCampaignCode(response.getBasicInfo().getCampaignCode());
                        request.setPayers(payerRequests);
                        return request;
                    }
                    return null;
                } else {
                    var request = new CampaignRequest();
                    request.setCampaignCode(response.getBasicInfo().getCampaignCode());
                    return request;
                }
            }).filter(Objects::nonNull).toList();
        if (CollectionUtils.isNotEmpty(campaignRequestByFixed)) {
            requests.addAll(campaignRequestByFixed);
        }
    }

    private void addCampaignRequestByRule(AutoGoods goods, OrderContext order, BasicUiModel basicUiModel, List<CampaignRequest> requests, List<CampaignResponse> parent, List<String> campaignCodeByUserInput, IPhase marketPhase) {
        val campaignRequestByRole = parent.stream()
            .filter(e -> Objects.nonNull(e.getBasicInfo()) && !campaignCodeByUserInput.contains(e.getBasicInfo().getCampaignCode()))
            .filter(e -> e.getCatePremiumDiscount().getPeriodList().stream()
                .flatMap(p -> p.getPayerList().stream())
                .anyMatch(c -> Objects.equals(CampaignDiscountValueTypeEnum.RULE.getCode(), c.getDiscountValueType())))
            .map(response -> {
                var request = new CampaignRequest();
                request.setCampaignCode(response.getBasicInfo().getCampaignCode());
                request.setPayers(Lists.newArrayList());
                response.getCatePremiumDiscount().getPeriodList().stream()
                    .filter(p -> CollectionUtils.isNotEmpty(p.getPayerList()))
                    .flatMap(p -> p.getPayerList().stream())
                    .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(payer -> "" + payer.getPayerType() + "-" + payer.getPayerCode() + "-" + payer.getPayerSubCode()))), ArrayList::new))
                    .forEach(payer -> {
                        String ruleCode = payer.getRuleCode();
                        var sourceInput = taylorProxy.fetchInput(FetchRuleDataRequest.of(ruleCode, bizDateService.getBizDate(basicUiModel.getScenario(), goods.getPlan().getEffectiveDate(), order)));
                        if (!sourceInput.available()) {
                            log.warn("Rule (CODE: {}) is not available.", ruleCode);
                            return;
                        }

                        UiModelToRuleConverterBuilder.BasicUiModelToRuleConverter uiModelToRuleConverter = uiModelToRuleConverterBuilder.getOne().selectedGoodsId(goods.getGoodsId()).build();
                        RuleCheckContext ruleCheckContext = RuleCheckContext.builder().orderContext(order).build();
                        if (marketPhase instanceof MarketPhase phase) {
                            ruleCheckContext.setMarketPhase(phase);
                        }
                        ruleCheckContext.setSelectedGoods(goods);
                        ruleCheckContext.setSelectedGoodsId(goods.getGoodsId());
                        val ruleFactorModel = uiModelToRuleConverter.convert(basicUiModel, new RuleFactorModel(), ruleCheckContext);
                        val fireRuleInput = ruleFactorModel.buildRuleFactorValue().toTaylorInput(sourceInput);
                        if (fireRuleInput == null) {
                            log.warn("Rule (CODE: {}) is null.", ruleCode);
                            return;
                        }
                        val result = taylorProxy.fire(fireRuleInput);
                        ofNullable(result).flatMap(r -> ofNullable(r.getMessages())).flatMap(m -> m.stream()
                                .filter(p -> Objects.equals(p.getActionType(), RuleActionType.DISCOUNT) && Objects.nonNull(p.getNumericValue()))
                                .map(p -> p.getNumericValue().divide(new BigDecimal(100))).findFirst())
                            .ifPresent(rate -> {
                                var payerRequest = new CampaignPayerRequest();
                                payerRequest.setPayerCode(payer.getPayerCode());
                                payerRequest.setPayerSubCode(payer.getPayerSubCode());
                                payerRequest.setPayerType(payer.getPayerType());
                                payerRequest.setDiscountRate(rate.stripTrailingZeros().toPlainString());
                                request.getPayers().add(payerRequest);
                            });
                    });
                return request;
            })
            .filter(campaignRequest -> CollectionUtils.isNotEmpty(campaignRequest.getPayers()))
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(campaignRequestByRole)) {
            requests.addAll(campaignRequestByRole);
        }
    }

    private void addCampaignRequestByUserInput(List<CampaignRequest> requests, List<CampaignResponse> userInput, List<CampaignItem> campaignInputs) {
        if (CollectionUtils.isNotEmpty(campaignInputs) && CollectionUtils.isNotEmpty(userInput)) {
            val campaignToMaps = campaignInputs.stream().collect(Collectors.toMap(CampaignItem::getCampaignCode, Function.identity()));
            val campaignRequestByUserInput = userInput.stream()
                .filter(e -> Objects.nonNull(e.getBasicInfo()) && campaignToMaps.containsKey(e.getBasicInfo().getCampaignCode()))
                .map(e -> getCampaignRequest(campaignToMaps, e)).toList();
            requests.addAll(campaignRequestByUserInput);
        }
    }

    @NotNull
    private CampaignRequest getCampaignRequest(Map<String, CampaignItem> campaignToMaps, CampaignResponse e) {
        var request = new CampaignRequest();
        request.setCampaignCode(e.getBasicInfo().getCampaignCode());
        val payers = e.getCatePremiumDiscount().getPeriodList().stream()
            .filter(p -> CollectionUtils.isNotEmpty(p.getPayerList()))
            .flatMap(p -> p.getPayerList().stream())
            .collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(payer -> "" + payer.getPayerType() + "-" + payer.getPayerCode()))), ArrayList::new));
        val payerRequests = mapBy(payers, pay -> {
            var payerRequest = new CampaignPayerRequest();
            payerRequest.setPayerCode(pay.getPayerCode());
            payerRequest.setPayerSubCode(pay.getPayerSubCode());
            payerRequest.setPayerType(pay.getPayerType());
            Optional.of(campaignToMaps.get(e.getBasicInfo().getCampaignCode())).ifPresent(x -> {
                Optional.ofNullable(x.getAmount()).ifPresent(a -> payerRequest.setDiscountAmount(a.toPlainString()));
                Optional.ofNullable(x.getRate()).ifPresent(a -> payerRequest.setDiscountRate(a.toPlainString()));
            });
            return payerRequest;
        });
        request.setPayers(payerRequests);
        return request;
    }

    /**
     * 如果一个promotion code同时匹配到了多个campaignCode，默认取list里的第一个
     */
    private String getCampaignCode(List<CampaignResponse> campaignResponses, String channelCode) {
        return campaignResponses
            .stream()
            .filter(campaign -> ofNullable(campaign.getPartnerManagements())
                .orElse(Collections.emptyList())
                .stream()
                .map(CampaignPartnerManagementResponse::getSalesChannelCode)
                .anyMatch(campaignChannelCode -> Objects.equals(campaignChannelCode, channelCode)))
            .map(CampaignResponse::getBasicInfo)
            .map(CampaignBasicInfoBase::getCampaignCode)
            .filter(StringUtils::isNotBlank)
            .findFirst()
            .orElseThrow(() -> CommonException.byError(QuotationErrorCode.PROMOTION_CODE_NOT_VALID));
    }

    //过滤campaign中和当前channel匹配的，然后返回对应的campaignCode和对应的getNewInsuredRule
    private Map<String, Object> getCampaignInfosByAgent(List<CampaignResponse> campaignResponse, String channelCode) {
        Map<String, Object> campaignInfos = new HashMap<>();
        ofNullable(campaignResponse).orElseGet(Collections::emptyList)
            .forEach(camp -> {
                if (ofNullable(camp.getPartnerManagements()).orElseGet(Collections::emptyList).stream().map(CampaignPartnerManagementResponse::getSalesChannelCode)
                    .anyMatch(campaignChannelCode -> Objects.equals(campaignChannelCode, channelCode))) {
                    val campaignNewInsuredRuleEnum = ofNullable(camp.getParticipationCriteria())
                        .flatMap(e -> ofNullable(e.getNewInsuredRule()));
                    campaignInfos.put(camp.getBasicInfo().getCampaignCode(), campaignNewInsuredRuleEnum.orElse(null));
                }
            });
        return campaignInfos;
    }

    private List<CampaignResponse> getCampaignResponse(AutoGoods goods, BasicUiModel basicUiModel, Long goodsId, Long planId) {

        val queryCampaignRequest = new QueryCampaignRequest();
        queryCampaignRequest.setPlanId(planId);
        queryCampaignRequest.setGoodsId(goodsId);
        log.info("campaignFeign.queryCampaignRelating request: planId: {}, goodsId: {}",
            planId, goodsId);
        return ofNullable(campaignService.queryCampaignRelating(queryCampaignRequest))
            .orElse(Collections.emptyList()).stream()
            .filter(camp -> campaignScenario(basicUiModel, camp))
            .filter(camp -> filterValidPeriodCampaign(camp, goods)).collect(Collectors.toList());
    }

    //根据promotionCode，和goodsId，planId查询campaign
    private List<CampaignResponse> getCampaignResponseByPromotionCode(Long goodsId, Long planId, String promotionCode, BasicUiModel basicUiModel, AutoGoods goods) {
        val queryCampaignRequest = new QueryCampaignRequest();
        queryCampaignRequest.setGoodsId(goodsId);
        queryCampaignRequest.setPlanId(planId);
        queryCampaignRequest.setPromotionCode(promotionCode);
        log.info("campaignFeign.queryCampaignRelating request: goodsId: {}, planId: {}, promotionCode: {}",
            goodsId, planId, promotionCode);
        List<CampaignResponse> campaignResponses = ofNullable(campaignService.queryCampaignRelating(queryCampaignRequest))
            .orElse(Collections.emptyList()).stream()
            //过滤符合当前application scenario的（NB/RENEWAL）
            .filter(camp -> campaignScenario(basicUiModel, camp))
            //过滤符合生效时间内的
            .filter(camp -> filterValidPeriodCampaign(camp, goods))
            .collect(Collectors.toList());

        //需要注意，如果是没有匹配到的campaign，需要抛出异常。
        //TODO 需要注意，没有匹配到的时候需要抛出异常
        throwIf(CollectionUtils.isEmpty(campaignResponses), () -> CommonException.byError(QuotationErrorCode.PROMOTION_CODE_NOT_VALID));
        return campaignResponses;
    }

    //过滤从campaign查询回来的活动的，判断对应的applicationScenario是否和当前场景的对应的上。（NB/RENEWAL）
    private boolean campaignScenario(BasicUiModel basicUiModel, CampaignResponse camp) {
        val basicInfo = camp.getBasicInfo();
        if (basicInfo == null || CollectionUtils.isEmpty(basicInfo.getApplicationScenarios())) {
            return true;
        }
        Integer code;
        if (basicUiModel.getScenario() != null && Objects.equals(basicUiModel.getScenario(), ScenarioTypeEnum.renew)) {
            code = SaPremiumCalculationBizTypeEnum.RENEWAL.getCode();
        } else {
            code = SaPremiumCalculationBizTypeEnum.NB.getCode();
        }
        return basicInfo.getApplicationScenarios().contains(code);
    }

    private List<CampaignRequest> builderCampaignRequestListByPromotion(String promotionCode, Map<String, Object> campaignInfos) {
        if (StringUtils.isBlank(promotionCode) && org.springframework.util.CollectionUtils.isEmpty(campaignInfos)) {
            return Collections.emptyList();
        }
        List<CampaignRequest> campaignRequests = new ArrayList<>();
        campaignInfos.forEach((code, rule) -> {
            val campaignRequest = new CampaignRequest();
            campaignRequest.setCampaignCode(code);
            campaignRequest.setCampaignParticipationPeriodNo(DEFAULT_CAMPAIGN_PARTICIPATION_PERIOD_NO);

            val promotionRequest = new PromotionRequest();
            promotionRequest.setPromotionCode(promotionCode);
            campaignRequest.setPromotions(Collections.singletonList(promotionRequest));
            campaignRequests.add(campaignRequest);
        });
        return campaignRequests;
    }

    private boolean filterByConditionRule(CampaignResponse campaign, BasicUiModel basicUiModel, AutoGoods goods, CommonConvertContext context, List<CampaignResponse> campaignResponseList, IPhase marketPhase) {
        val conditionRuleCodes = Optional.ofNullable(campaign.getCatePremiumDiscount())
            .map(CampaignCatePremiumDiscountResponse::getPeriodList)
            .orElseGet(Lists::newArrayList)
            .stream().map(CampaignPremiumDiscountPeriodResponse::getPayerList)
            .flatMap(Collection::stream)
            .map(CampaignPremiumDiscountPayerResponse::getConditionRuleCode)
            .filter(Objects::nonNull)
            .distinct()
            .toList();
        if (CollectionUtils.isEmpty(conditionRuleCodes)) {
            return true;
        }
        return conditionRuleCodes.stream().allMatch(ruleCode -> {

            UiModelToRuleConverterBuilder.BasicUiModelToRuleConverter uiModelToRuleConverter = uiModelToRuleConverterBuilder.getOne().selectedGoodsId(goods.getGoodsId()).build();
            RuleCheckContext ruleCheckContext = RuleCheckContext.builder().orderContext(context.getOrderContext()).build();
            if (marketPhase instanceof MarketPhase phase) {
                ruleCheckContext.setMarketPhase(phase);
            }
            ruleCheckContext.setSelectedGoodsId(goods.getGoodsId());
            ruleCheckContext.setSelectedGoods(goods);
            ruleCheckContext.setTrailContextOpt(context.getTrailContextOpt());
            val ruleFactorModel = uiModelToRuleConverter.convert(basicUiModel, new RuleFactorModel(), ruleCheckContext);
            var sourceInput = taylorProxy.fetchInput(ruleCode);
            if (!sourceInput.available()) {
                log.warn("Rule (CODE: {}) is not available.", ruleCode);
                return false;
            }
            campaignResponseToRuleFactorModel(ruleFactorModel, campaignResponseList);
            val fireRuleInput = ruleFactorModel.buildRuleFactorValue().toTaylorInput(sourceInput);
            if (fireRuleInput == null) {
                log.warn("Rule (CODE: {}) is null.", ruleCode);
                return false;
            }
            val result = taylorProxy.fire(fireRuleInput);
            boolean accept = false;
            val decisionMessages = ofNullable(result)
                .map(RuleResult::getMessages)
                .orElseGet(Lists::newArrayList)
                .stream()
                .filter(x -> Objects.equals(x.getActionType(), RuleActionType.GENERAL_DECISION))
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(decisionMessages)) {
                accept = decisionMessages.stream()
                    .allMatch(x -> x.getDecisionType() == DecisionType.ACCEPT);
            }
            doIf(!accept, () -> log.info("Campaign {} removed by rule {}", campaign.getBasicInfo().getCampaignCode(), ruleCode));
            return accept;
        });
    }

    //过滤投保时间在活动生效期时间内的，然后生效时间在活动有效期内（包含宽限期）
    boolean filterValidPeriodCampaign(CampaignResponse campaignResponse, AutoGoods goods) {

        var insureDate = goods.getInsureDate() == null ? dateService.getBizDate() : goods.getInsureDate();
        Date effectiveDate = goods.getPlan().getEffectiveDate();

        Date startTime = campaignResponse.getBasicInfo().getStartTime();
        Date expireTime = campaignResponse.getBasicInfo().getExpireTime();
        Integer gracePeriodValue = campaignResponse.getBasicInfo().getGracePeriodValue();

        BooleanSupplier insureDateSupplier = () -> startTime.before(insureDate) && expireTime.after(insureDate);
        //在宽限期内
        //经过沟通，这里只考虑EffectiveDate能拿到的（配置成userInput的）
        BooleanSupplier gracePeriodSupplier = () -> effectiveDate != null && gracePeriodValue != null && startTime.before(effectiveDate) && DateUtils.addDays(expireTime, gracePeriodValue).after(effectiveDate);
        return insureDateSupplier.getAsBoolean() || gracePeriodSupplier.getAsBoolean();
    }

    private void campaignResponseToRuleFactorModel(RuleFactorModel ruleFactorModel, List<CampaignResponse> campaignResponseList) {
        if (CollectionUtils.isEmpty(campaignResponseList)) {
            return;
        }

        List<RuleFactorModel.CampaignDiscount> campaignDiscountList = new ArrayList<>();
        campaignResponseList.forEach(campaignResponse ->
            Optional.ofNullable(campaignResponse.getBasicInfo()).map(CampaignBasicInfoResponse::getCampaignCode).ifPresent(code -> {
                RuleFactorModel.CampaignDiscount campaignDiscount = new RuleFactorModel.CampaignDiscount();
                campaignDiscount.setCampaignCode(code);
                campaignDiscountList.add(campaignDiscount);
            }));
        ruleFactorModel.setCampaignDiscountList(campaignDiscountList);
    }

}
