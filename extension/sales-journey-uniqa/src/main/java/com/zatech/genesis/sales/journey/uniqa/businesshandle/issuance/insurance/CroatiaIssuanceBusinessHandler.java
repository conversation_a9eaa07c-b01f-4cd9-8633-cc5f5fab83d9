/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.insurance;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.croatia.CroatiaUiModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@UIModelBusinessHandler(name = "issuance", kind = BusinessHandlerKind.policy, desc = "Croatia auto policy issuance business handler", tags =
    Consts.CROATIA)
public class CroatiaIssuanceBusinessHandler implements IAuthBusinessHandler<PolicyPhase, CroatiaUiModel,
    IssuanceResult>, IBusinessHandleTrailSupport<PolicyPhase, IssuanceResult, BasicUiModel> {

    @Autowired
    private BaseIssuanceBusinessHandler baseHandler;

    @Override
    public PolicyPhase[] supportedPhases() {
        return new PolicyPhase[]{PolicyPhase.insurance};
    }

    @Override
    public Result onStop(PolicyPhase phase, CroatiaUiModel uiModel, IssuanceResult issuanceResult, BusinessHandleContext context) {
        return baseHandler.onStop(uiModel, issuanceResult, context);
    }

    @Override
    public FlowStrategy handle(PolicyPhase phase, CroatiaUiModel uiModel, IssuanceResult issuanceResult, BusinessHandleContext context) {
        baseHandler.handle(phase, uiModel, issuanceResult, context);
        return FlowStrategy.Stop;
    }

}
