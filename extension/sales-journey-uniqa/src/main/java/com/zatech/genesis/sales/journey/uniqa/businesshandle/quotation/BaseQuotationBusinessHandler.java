/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.genesis.market.api.calculate.request.sapremium.SaPremiumCalcInitialPeriodRequest;
import com.zatech.genesis.metadata.api.currency.CurrencyConversionRateRequest;
import com.zatech.genesis.metadata.api.currency.CurrencyConversionRateResp;
import com.zatech.genesis.metadata.client.currency.CurrencyService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.param.QuotationParam;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.result.BatchQuotationResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.Goods;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.IssuanceCheckService;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result.QuotationResultConverter;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service.QuoteCheckService;
import com.zatech.genesis.sales.journey.uniqa.converter.GreenCardRequestConverter;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.goodsmatrix.BaseGoodsMatrixDataProvider;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.quotation.ConvertQuotationRequestContext;
import com.zatech.octopus.framework.bizdate.BusinessDateService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BaseQuotationBusinessHandler {

    @Autowired
    private Converters converters;

    @Autowired
    private QuotationResultConverter quotationResultConverter;

    @Autowired
    private Environment environment;

    @Autowired
    private CurrencyService currencyService;

    @Autowired
    private BusinessDateService dateService;

    @Autowired
    private GreenCardRequestConverter greenCardRequestConverter;

    @Autowired
    private IssuanceCheckService issuanceCheckService;

    @Autowired
    private QuoteCheckService quoteCheckService;

    public static final String DEPLOY_ENV = "DEPLOY_ENV";

    public static final List<String> MOCK_ENV = List.of("verify");

    public void handle(MarketPhase marketPhase, BasicUiModel basicUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        issuanceCheckService.checkIfLockByUser(new ContextHolder(businessHandleContext.getOrder()));
        List<Long> quotationGoodsIdList;
        if (marketPhase.equals(MarketPhase.quickQuotation)) {
            quotationGoodsIdList = Stream.of(basicUiModel.getCommercialInsuranceGoods(), basicUiModel.getTrafficCompulsoryInsuranceGoods()).filter(Objects::nonNull).map(Goods::getGoodsId).toList();
            quotationGoodsIdList = filterNotCanBuyMhull(basicUiModel, quotationGoodsIdList);
        } else if (marketPhase.equals(MarketPhase.quotation)) {
            quoteCheckService.check(businessHandleContext.getOrder().getOrderNo(), basicUiModel);
            quotationGoodsIdList = basicUiModel.selectedGoodsId();
        } else {
            quotationGoodsIdList = basicUiModel.canSelectGoodsId();
        }
        Date exchangeDate = dateService.getBizDate();
        List<SaPremiumCalcInitialPeriodRequest> premiumCurrencyCalcInitialPeriodRequests = quotationGoodsIdList.stream().map(goodsId -> {
            var context = new ConvertQuotationRequestContext(businessHandleContext);
            context.setMarketPhase(marketPhase);
            context.setSelectedGoodsId(goodsId);
            context.setSelectedGoods(basicUiModel.selectedGoods(goodsId));
            SaPremiumCalcInitialPeriodRequest premiumCalcInitialPeriodRequest = converters.convert(basicUiModel, new SaPremiumCalcInitialPeriodRequest(), context);
            AutoGoods autoGoods = basicUiModel.selectedGoods(goodsId);
            CurrencyEnum premiumCurrency = autoGoods.getPlan().getPremiumCurrency();
            premiumCalcInitialPeriodRequest.setUnqueKey(premiumCurrency.name());
            CurrencyEnum saCurrency = autoGoods.getPlan().getSaCurrency();
            if (!Objects.equals(saCurrency, premiumCurrency)) {
                premiumCalcInitialPeriodRequest.setPremiumCurrencyExchangeRate(getExchangeRate(saCurrency, premiumCurrency, exchangeDate));
            }
            return premiumCalcInitialPeriodRequest;
        }).toList();
        List<SaPremiumCalcInitialPeriodRequest> baseCurrencyRequestList = buildBaseCurrencyRequests(marketPhase, basicUiModel, quotationParam, premiumCurrencyCalcInitialPeriodRequests, exchangeDate);
        List<SaPremiumCalcInitialPeriodRequest> unionRequestList = ListUtils.union(premiumCurrencyCalcInitialPeriodRequests, baseCurrencyRequestList);
        quotationParam.setQuotationRequest(unionRequestList);

        if (Objects.nonNull(basicUiModel.getTrafficCompulsoryInsuranceGoods()) && Objects.equals(true, basicUiModel.getTrafficCompulsoryInsuranceGoods().getSelected()) && Objects.nonNull(basicUiModel.getTrafficCompulsoryInsuranceGoods().getElements()) && Objects.nonNull(basicUiModel.getTrafficCompulsoryInsuranceGoods().getElements().getIssueWithGreenCard())) {
            quotationParam.setGreenCard(basicUiModel.getTrafficCompulsoryInsuranceGoods().getElements().getIssueWithGreenCard());
            if (quotationParam.isGreenCard()) {
                Long goodsId = basicUiModel.getTrafficCompulsoryInsuranceGoods().getGoodsId();
                Optional<SaPremiumCalcInitialPeriodRequest> mtplGoodsRequest = premiumCurrencyCalcInitialPeriodRequests.stream().filter(saPremiumCalcInitialPeriodRequest -> saPremiumCalcInitialPeriodRequest.getGoodsId().equals(goodsId)).findFirst();
                if (mtplGoodsRequest.isPresent()) {
                    SaPremiumCalcInitialPeriodRequest saPremiumCalcInitialPeriodRequest = mtplGoodsRequest.get();
                    quotationParam.setGreenCardRequests(greenCardRequestConverter.buildGreenCardRequestBySaPremium(saPremiumCalcInitialPeriodRequest));
                }
            }
        }
    }

    private static List<Long> filterNotCanBuyMhull(BasicUiModel basicUiModel, List<Long> quotationGoodsIdList) {
        quotationGoodsIdList = quotationGoodsIdList.stream().filter(goodId -> !Objects.equals(goodId, Optional.ofNullable(basicUiModel.getCommercialInsuranceGoods()).map(Goods::getGoodsId).orElse(null)) || !BaseGoodsMatrixDataProvider.notCanBuyMhull(basicUiModel)).toList();
        return quotationGoodsIdList;
    }

    private List<SaPremiumCalcInitialPeriodRequest> buildBaseCurrencyRequests(MarketPhase marketPhase, BasicUiModel basicUiModel, QuotationParam quotationParam, List<SaPremiumCalcInitialPeriodRequest> premiumCurrencyCalcInitialPeriodRequests, Date exchangeDate) {
        List<SaPremiumCalcInitialPeriodRequest> baseCurrencyRequestList = premiumCurrencyCalcInitialPeriodRequests.stream()
            .filter(x -> needMultiCurrency(basicUiModel, x.getGoodsId(), marketPhase)).map(x -> {
                SaPremiumCalcInitialPeriodRequest saPremiumCalcInitialPeriodRequest = StaticJsonParser.copyObject(x, SaPremiumCalcInitialPeriodRequest.class);
                AutoGoods autoGoods = basicUiModel.selectedGoods(x.getGoodsId());
                CurrencyEnum baseCurrency = autoGoods.getPlan().getBaseCurrency();
                CurrencyEnum premiumCurrency = autoGoods.getPlan().getPremiumCurrency();
                CurrencyEnum saCurrency = autoGoods.getPlan().getSaCurrency();
                CurrencyEnum displayCurrency = basicUiModel.getDisplayCurrency();
                String exchangeRate;
                if (Objects.equals(premiumCurrency, baseCurrency) && Objects.nonNull(displayCurrency) && !Objects.equals(premiumCurrency, basicUiModel.getDisplayCurrency())) {
                    exchangeRate = getExchangeRate(premiumCurrency, displayCurrency, exchangeDate);
                    saPremiumCalcInitialPeriodRequest.setUnqueKey(displayCurrency.name());
                } else {
                    exchangeRate = getExchangeRate(saCurrency, baseCurrency, exchangeDate);
                    saPremiumCalcInitialPeriodRequest.setUnqueKey(baseCurrency.name());
                }
                saPremiumCalcInitialPeriodRequest.setPremiumCurrencyExchangeRate(null);
                saPremiumCalcInitialPeriodRequest.setPremium2BaseCurrencyExchangeRate(exchangeRate);

                return saPremiumCalcInitialPeriodRequest;
            }).collect(Collectors.toList());
        buildExchangeInfo(basicUiModel, quotationParam, exchangeDate, baseCurrencyRequestList);
        return baseCurrencyRequestList;
    }

    private void buildExchangeInfo(BasicUiModel basicUiModel, QuotationParam quotationParam, Date exchangeDate, List<SaPremiumCalcInitialPeriodRequest> baseCurrencyRequestList) {
        if (CollectionUtils.isNotEmpty(baseCurrencyRequestList)) {
            CurrencyEnum displayCurrency = basicUiModel.getDisplayCurrency();
            CurrencyEnum baseCurrency = basicUiModel.selectedGoods(basicUiModel.selectedGoodsId().stream().filter(Objects::nonNull).findFirst().orElse(null)).getPlan().getBaseCurrency();
            quotationParam.setExchangeDate(exchangeDate);
            quotationParam.setExchangeRate(getExchangeRate(displayCurrency, baseCurrency, exchangeDate));
        }
    }

    private boolean needMultiCurrency(BasicUiModel basicUiModel, Long goodsId, MarketPhase marketPhase) {
        AutoGoods autoGoods = basicUiModel.selectedGoods(goodsId);
        CurrencyEnum baseCurrency = autoGoods.getPlan().getBaseCurrency();
        CurrencyEnum premiumCurrency = autoGoods.getPlan().getPremiumCurrency();
        CurrencyEnum displayCurrency = basicUiModel.getDisplayCurrency();
        //单买mtpl的时候不需要多币种
        boolean onlyBuyMtplAndPaGoods = basicUiModel.selectedGoodsId().stream().allMatch(x -> Stream.of(basicUiModel.getTrafficCompulsoryInsuranceGoods(), basicUiModel.getPassengerAccidentGoods()).filter(Objects::nonNull).map(Goods::getGoodsId).anyMatch(g -> Objects.equals(x, g)));
        return Objects.nonNull(displayCurrency) && Objects.nonNull(baseCurrency) && (!Objects.equals(premiumCurrency, baseCurrency) || !Objects.equals(premiumCurrency, displayCurrency)) && (!onlyBuyMtplAndPaGoods || Objects.equals(marketPhase, MarketPhase.previewQuotation));
    }

    public Result onContinueSucceed(Result result, MarketPhase marketPhase, BasicUiModel basicUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        if (result instanceof BatchQuotationResult batchQuotationResult) {
            return quotationResultConverter.convert(basicUiModel, batchQuotationResult.getOriginResult());
        } else {
            return result;
        }
    }

    public Result onContinueError(Exception e, MarketPhase marketPhase, BasicUiModel basicUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        throw CommonException.byErrorAndCause(CommonBusinessHandleErrorCodes.QUOTATION_SYS_FAIL, e);
    }

    public BatchQuotationResult mockResult() {
        String mockResult = "{\"basicCurrencyResult\":{\"currency\":\"EUR\",\"total\":{\"coverageTotalFinalPremium\":\"9654.1091\"},\"commercialInsuranceGoods\":{\"goodsId\":921476140122112,\"coverageTotalFinalPremium\":\"236.52\"},\"trafficCompulsoryInsuranceGoods\":{\"goodsId\":1217295619735552,\"coverageTotalFinalPremium\":\"9417.5891\"}},\"otherCurrencyResult\":{\"currency\":\"RSD\",\"total\":{\"coverageTotalFinalPremium\":\"9654.1105544\"},\"commercialInsuranceGoods\":{\"goodsId\":921476140122112,\"coverageTotalFinalPremium\":\"236.5214544\"},\"trafficCompulsoryInsuranceGoods\":{\"goodsId\":1217295619735552,\"coverageTotalFinalPremium\":\"9417.5891\"}}}";

        return StaticJsonParser.fromJsonString(mockResult, BatchQuotationResult.class);
    }

    private String getExchangeRate(CurrencyEnum sourceCurrency, CurrencyEnum targetCurrency, Date exchangeDate) {
        if (Objects.equals(sourceCurrency, targetCurrency)) {
            return BigDecimal.ONE.toPlainString();
        }
        CurrencyConversionRateRequest currencyConversionRateRequest = new CurrencyConversionRateRequest();
        currencyConversionRateRequest.setOriginalCurrency(sourceCurrency);
        currencyConversionRateRequest.setConversionDate(exchangeDate);
        currencyConversionRateRequest.setTargetCurrency(targetCurrency);
        CurrencyConversionRateResp currencyConversionRateResp = currencyService.queryCurrencyConversionRate(currencyConversionRateRequest);
        return Optional.ofNullable(currencyConversionRateResp).map(CurrencyConversionRateResp::getRate).orElse(null);
    }

}
