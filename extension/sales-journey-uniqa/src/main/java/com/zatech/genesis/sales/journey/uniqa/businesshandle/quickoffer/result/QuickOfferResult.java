/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer.result;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * @Author: weizhen.kong
 */
@Getter
@Setter
@Accessors(chain = true)
public class QuickOfferResult implements Result {

    private String fileUniqueCode;

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }

}