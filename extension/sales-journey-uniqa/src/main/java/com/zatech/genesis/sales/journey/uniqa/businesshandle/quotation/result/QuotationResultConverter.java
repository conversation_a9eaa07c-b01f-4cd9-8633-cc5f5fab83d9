/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result;

import com.zatech.genesis.sales.journey.share.tools.BeanUtil;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.zatech.gaia.resource.biz.NcdCalculationBasisEnum;
import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.PayerTypeEnum;
import com.zatech.gaia.resource.components.enums.market.BizTopicEnum;
import com.zatech.gaia.resource.components.enums.product.AdditionalTypeEnum;
import com.zatech.gaia.resource.components.enums.product.LoadingMethodEnum;
import com.zatech.gaia.resource.components.enums.product.PremiumDiscountTypeEnum;
import com.zatech.genesis.market.api.calculate.response.sapremium.CalcSaPremiumMultiPeriodResponse;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumCalcProductResponse;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumCalcResponse;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumLiabilityResponse;
import com.zatech.genesis.market.api.split.response.LiabilityExtraPremiumDetailResponse;
import com.zatech.genesis.market.api.split.response.ProductDiscountPremiumDetailResponse;
import com.zatech.genesis.metadata.api.precision.response.PrecisionResponse;
import com.zatech.genesis.metadata.client.currency.CurrencyConversionRequest;
import com.zatech.genesis.policy.api.base.Currency;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.reflect.Reflector;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.reflect.ReflectorFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.reflect.Reflectors;
import com.zatech.genesis.portal.toolbox.share.EnumHelper;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.result.BatchQuotationResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoLiabilities;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPlan;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProduct;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProducts;
import com.zatech.genesis.sales.journey.client.biz.common.model.Goods;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.Plan;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.Liability;
import com.zatech.genesis.sales.journey.client.biz.common.provider.currencyconversion.param.CurrencyConversionParam;
import com.zatech.genesis.sales.journey.client.biz.common.provider.currencyconversion.result.CurrencyConversionResult;
import com.zatech.genesis.sales.journey.client.biz.common.util.CurrencyConversionService;
import com.zatech.genesis.sales.journey.integration.policy.GreenCardAdapter;
import com.zatech.genesis.sales.journey.integration.policy.dto.PolicyCalcSaResponse;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.val;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import static com.za.cqrs.util.Functions.doIfPresent;
import static com.za.cqrs.util.Functions.mapBy;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;

@Component
public class QuotationResultConverter {

    @Autowired
    private ReflectorFactory reflectorFactory;

    @Autowired
    private CurrencyConversionService currencyConversionService;

    @Autowired
    private GreenCardAdapter cardAdapter;

    public BatchQuotationResult convert(BasicUiModel basicUiModel, BatchQuotationResult.OriginResult originResult) {
        BatchQuotationResult batchQuotationResult = new BatchQuotationResult();
        if (CollectionUtils.isEmpty(originResult.getCalcSaPremiumMultiPeriods())) {
            return batchQuotationResult;
        }
        batchQuotationResult.setOriginResult(originResult);
        buildPremiumCurrencyResult(basicUiModel, batchQuotationResult);
        buildBaseCurrencyResult(basicUiModel, batchQuotationResult);
        buildDisplayCurrencyResult(basicUiModel, batchQuotationResult);
        batchQuotationResult.setExchangeDate(originResult.getExchangeDate());
        batchQuotationResult.setExchangeRate(originResult.getExchangeRate());
        return batchQuotationResult;
    }

    public BatchQuotationResult convertRenew(BasicUiModel basicUiModel, List<CalcSaPremiumMultiPeriodResponse> saPremiumMultiPeriodResponses) {
        BatchQuotationResult.OriginResult originResult = new BatchQuotationResult.OriginResult();
        originResult.setCalcSaPremiumMultiPeriods(saPremiumMultiPeriodResponses);
        return convert(basicUiModel, originResult);
    }

    public BatchQuotationResult convert(BasicUiModel basicUiModel, PolicyCalcSaResponse policyCalcSaResponse) {
        List<CalcSaPremiumMultiPeriodResponse> finalCalcSaPremiumMultiPeriodList = buildCalcSaPremiumMultiPeriods(Stream.of(policyCalcSaResponse.getBaseCurrencyPremiumResponse(), policyCalcSaResponse).toList(), QuotationResultConverter::fillCurrencyToUnqueKey);
        BatchQuotationResult.OriginResult originResult = new BatchQuotationResult.OriginResult();
        originResult.setCalcSaPremiumMultiPeriods(finalCalcSaPremiumMultiPeriodList);
        return convert(basicUiModel, originResult);
    }

    private <T extends CalcSaPremiumMultiPeriodResponse> List<CalcSaPremiumMultiPeriodResponse> buildCalcSaPremiumMultiPeriods(List<T> calcSaPremiumMultiPeriodResponses, Consumer<CalcSaPremiumMultiPeriodResponse> currencyConverter) {
        return calcSaPremiumMultiPeriodResponses.stream()
            .filter(Objects::nonNull)
            .filter(e -> e.getCoverageTotalCalcResponse() != null)
            .peek(currencyConverter)
            .map(e -> BeanUtil.copyProperties(e, CalcSaPremiumMultiPeriodResponse.class))
            .toList();
    }

    public BatchQuotationResult convert(BasicUiModel basicUiModel, IssuanceResponse issuanceResponse) {
        PolicyCalcSaResponse policyCalcSaResponse = ofNullable(issuanceResponse)
            .map(IssuanceResponse::getFirsthandPremiumInfo)
            .map(e -> StaticJsonParser.fromJsonString(e, PolicyCalcSaResponse.class))
            .orElse(null);

        if (policyCalcSaResponse == null) {
            return new BatchQuotationResult();
        }
        CurrencyEnum baseCurrency = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getBaseCurrency).orElse(null);
        CurrencyEnum premiumCurrency = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getPremiumCurrency).orElse(null);
        Date saToPremiumExchangeRateDate = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getSaToPremiumExchangeRateDate).orElse(null);
        String saToPremiumExchangeRate = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getSaToPremiumExchangeRate).orElse(null);

        String premiumToBaseExchangeRate = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getPremiumToBaseExchangeRate).orElse(null);
        Date toBaseExchangeRateDate = ofNullable(issuanceResponse.getMultiCurrency()).map(Currency::getToBaseExchangeRateDate).orElse(null);

        policyCalcSaResponse.fillCurrencyToUnqueKey(baseCurrency, premiumCurrency);

        List<CalcSaPremiumMultiPeriodResponse> finalCalcSaPremiumMultiPeriodList = Stream.of(policyCalcSaResponse.getBaseCurrencyPremiumResponse(), policyCalcSaResponse).filter(Objects::nonNull).toList();

        CurrencyEnum displayCurrency = basicUiModel.getDisplayCurrency();
        String exchangeRate = displayCurrency == premiumCurrency ? premiumToBaseExchangeRate : saToPremiumExchangeRate;
        Date exchangeDate = displayCurrency == premiumCurrency ? toBaseExchangeRateDate : saToPremiumExchangeRateDate;

        BatchQuotationResult.OriginResult originResult = new BatchQuotationResult.OriginResult();
        cardAdapter.queryByIssuance(issuanceResponse).ifPresent(gc -> {
            originResult.setGreenCardFee(gc.getGreenCardFee());
        });

        originResult.setExchangeRate(exchangeRate);
        originResult.setExchangeDate(exchangeDate);

        originResult.setCalcSaPremiumMultiPeriods(finalCalcSaPremiumMultiPeriodList);
        return convert(basicUiModel, originResult);
    }

    private static void fillCurrencyToUnqueKey(CalcSaPremiumMultiPeriodResponse e) {
        Optional.of(e)
            .map(CalcSaPremiumMultiPeriodResponse::getCoverageTotalCalcResponse)
            .map(SaPremiumCalcResponse::getPremiumCurrency)
            .map(Enum::name)
            .ifPresent(e.getCoverageTotalCalcResponse()::setUnqueKey);
    }

    private void buildPremiumCurrencyResult(BasicUiModel basicUiModel, BatchQuotationResult batchQuotationResult) {
        BatchQuotationResult.QuotationResult premiumCurrencyResult = new BatchQuotationResult.QuotationResult();
        List<CalcSaPremiumMultiPeriodResponse> calcSaPremiumMultiPeriodResponses = batchQuotationResult.getOriginResult().getCalcSaPremiumMultiPeriods()
            .stream()
            .filter(e -> matchCurrency(e, basicUiModel))
            .collect(toList());
        calcSaPremiumMultiPeriodResponses.stream().findFirst().ifPresent(x -> {
            CurrencyEnum premiumCurrency = ofNullable(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()))
                .map(AutoGoods::getPlan)
                .map(Plan::getPremiumCurrency)
                .orElse(null);
            premiumCurrencyResult.setCurrency(premiumCurrency);
        });
        buildResult(batchQuotationResult.getOriginResult().getGreenCardFee(), basicUiModel, calcSaPremiumMultiPeriodResponses, premiumCurrencyResult);
        handleTotal(premiumCurrencyResult);
        batchQuotationResult.setPremiumCurrencyResult(premiumCurrencyResult);
    }

    private static boolean matchCurrency(CalcSaPremiumMultiPeriodResponse calcSaPremiumMultiPeriodResponse, BasicUiModel basicUiModel) {
        return ofNullable(calcSaPremiumMultiPeriodResponse)
            .map(CalcSaPremiumMultiPeriodResponse::getCoverageTotalCalcResponse)
            .map(SaPremiumCalcResponse::getGoodsId)
            .map(basicUiModel::selectedGoods)
            .map(AutoGoods::getPlan)
            .map(AutoPlan::getPremiumCurrency)
            .map(Enum::name)
            .map(x -> Optional.of(calcSaPremiumMultiPeriodResponse).map(CalcSaPremiumMultiPeriodResponse::getCoverageTotalCalcResponse).map(SaPremiumCalcResponse::getUnqueKey).map(x::equals).orElse(false))
            .orElse(false);
    }

    private void buildBaseCurrencyResult(BasicUiModel basicUiModel, BatchQuotationResult batchQuotationResult) {
        //所有的goods保费币和本位币一样，这个时候不需要返回本位币结果
        if (!CollectionUtils.isEmpty(batchQuotationResult.getOriginResult().getCalcSaPremiumMultiPeriods()) && batchQuotationResult.getOriginResult().getCalcSaPremiumMultiPeriods().stream().allMatch(x -> notNeedBaseCurrencyResult(basicUiModel, x))) {
            return;
        }
        BatchQuotationResult.QuotationResult baseCurrencyResult = new BatchQuotationResult.QuotationResult();
        List<CalcSaPremiumMultiPeriodResponse> calcSaPremiumMultiPeriodResponses = batchQuotationResult.getOriginResult().getCalcSaPremiumMultiPeriods().stream()
            .filter(x -> Objects.nonNull(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId())))
            .filter(x -> Objects.nonNull(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()).getPlan()))
            .filter(x -> Objects.nonNull(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()).getPlan().getBaseCurrency()) && Objects.equals(x.getCoverageTotalCalcResponse().getUnqueKey(), basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()).getPlan().getBaseCurrency().name()))
            .collect(toList());
        calcSaPremiumMultiPeriodResponses.stream().findFirst().flatMap(x -> Optional.ofNullable(x.getCoverageTotalCalcResponse())
            .map(SaPremiumCalcResponse::getUnqueKey)).ifPresent(key -> baseCurrencyResult.setCurrency(EnumHelper.nameOf(key, CurrencyEnum.class)));
        buildResult(batchQuotationResult.getOriginResult().getGreenCardFee(), basicUiModel, calcSaPremiumMultiPeriodResponses, baseCurrencyResult);
        handleTotal(baseCurrencyResult);
        batchQuotationResult.setBaseCurrencyResult(baseCurrencyResult);
    }

    private boolean notNeedBaseCurrencyResult(BasicUiModel basicUiModel, CalcSaPremiumMultiPeriodResponse x) {
        return Optional.ofNullable(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()))
            .map(AutoGoods::getPlan)
            .map(p -> Objects.isNull(p.getBaseCurrency()) || Objects.equals(p.getBaseCurrency(), p.getPremiumCurrency()))
            .orElse(false);
    }

    private void buildDisplayCurrencyResult(BasicUiModel basicUiModel, BatchQuotationResult batchQuotationResult) {
        BatchQuotationResult.QuotationResult displayCurrencyResult = new BatchQuotationResult.QuotationResult();
        List<CalcSaPremiumMultiPeriodResponse> calcSaPremiumMultiPeriodResponses = batchQuotationResult.getOriginResult().getCalcSaPremiumMultiPeriods().stream()
            .filter(x -> Objects.nonNull(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId())))
            .filter(x -> Objects.nonNull(basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()).getPlan()))
            .filter(x -> filterDisplayCurrency(basicUiModel, x))
            .collect(toList());
        calcSaPremiumMultiPeriodResponses.stream().findFirst().ifPresent(x -> {
            displayCurrencyResult.setCurrency(EnumHelper.nameOf(x.getCoverageTotalCalcResponse().getUnqueKey(), CurrencyEnum.class));
        });
        String greenCardFee = buildDisplayCurrencyGreenCard(basicUiModel, batchQuotationResult, displayCurrencyResult);
        buildResult(greenCardFee, basicUiModel, calcSaPremiumMultiPeriodResponses, displayCurrencyResult);
        handleTotal(displayCurrencyResult);
        batchQuotationResult.setDisplayCurrencyResult(displayCurrencyResult);
    }

    private String buildDisplayCurrencyGreenCard(BasicUiModel basicUiModel, BatchQuotationResult batchQuotationResult, BatchQuotationResult.QuotationResult displayCurrencyResult) {
        String greenCardFee = null;
        boolean greenCardFeeNeedExchange = ofNullable(basicUiModel)
            .map(BasicUiModel::getDisplayCurrency)
            .filter(displayCurrency -> ofNullable(basicUiModel.getTrafficCompulsoryInsuranceGoods())
                .map(AutoGoods::getPlan)
                .map(AutoPlan::getBaseCurrency)
                .filter(baseCurrency -> !Objects.equals(displayCurrency, baseCurrency))
                .isPresent())
            .isPresent();
        if (!greenCardFeeNeedExchange) {
            return batchQuotationResult.getOriginResult().getGreenCardFee();
        }
        if (StringUtils.hasText(batchQuotationResult.getOriginResult().getGreenCardFee())) {
            CurrencyConversionRequest currencyConversionRequest = getCurrencyConversionRequest(basicUiModel, batchQuotationResult, displayCurrencyResult);
            CurrencyConversionParam currencyConversionParam = new CurrencyConversionParam();
            currencyConversionParam.setAmountType(BizTopicEnum.PREMIUM);
            CurrencyConversionParam.CurrencyConversionItem currencyConversionItem = getCurrencyConversionItem(currencyConversionRequest);
            currencyConversionParam.setItems(List.of(currencyConversionItem));
            PrecisionResponse precisionResponse = currencyConversionService.getPrecisionResponse(currencyConversionParam, currencyConversionRequest.getTargetCurrency());
            CurrencyConversionResult.CurrencyConversionResultItem currencyConversionResultItem = currencyConversionService.currencyConversion(currencyConversionRequest, precisionResponse.getScale(), RoundingMode.valueOf(precisionResponse.getRoundingMode()));
            greenCardFee = currencyConversionResultItem.getTargetAmount();
        }
        return greenCardFee;
    }

    private CurrencyConversionParam.CurrencyConversionItem getCurrencyConversionItem(CurrencyConversionRequest currencyConversionRequest) {
        CurrencyConversionParam.CurrencyConversionItem currencyConversionItem = new CurrencyConversionParam.CurrencyConversionItem();
        currencyConversionItem.setConversionDate(currencyConversionRequest.getConversionDate());
        currencyConversionItem.setOriginalCurrency(currencyConversionRequest.getOriginalCurrency());
        currencyConversionItem.setTargetCurrency(currencyConversionRequest.getTargetCurrency());
        currencyConversionItem.setOriginalAmount(currencyConversionRequest.getOriginalAmount().toPlainString());
        return currencyConversionItem;
    }

    private CurrencyConversionRequest getCurrencyConversionRequest(BasicUiModel basicUiModel, BatchQuotationResult batchQuotationResult, BatchQuotationResult.QuotationResult displayCurrencyResult) {
        CurrencyConversionRequest currencyConversionRequest = new CurrencyConversionRequest();
        currencyConversionRequest.setOriginalCurrency(basicUiModel.getTrafficCompulsoryInsuranceGoods().getPlan().getBaseCurrency());
        currencyConversionRequest.setTargetCurrency(displayCurrencyResult.getCurrency());
        currencyConversionRequest.setConversionDate(batchQuotationResult.getOriginResult().getExchangeDate());
        currencyConversionRequest.setOriginalAmount(new BigDecimal(batchQuotationResult.getOriginResult().getGreenCardFee()));
        return currencyConversionRequest;
    }

    private static boolean filterDisplayCurrency(BasicUiModel basicUiModel, CalcSaPremiumMultiPeriodResponse x) {
        boolean onlyBuyMtplAndPaGoods = basicUiModel.selectedGoodsId().stream().allMatch(a -> Stream.of(basicUiModel.getTrafficCompulsoryInsuranceGoods(), basicUiModel.getPassengerAccidentGoods()).filter(Objects::nonNull).map(Goods::getGoodsId).anyMatch(g -> Objects.equals(a, g)));
        return !onlyBuyMtplAndPaGoods && Objects.nonNull(basicUiModel.getDisplayCurrency()) && Objects.equals(x.getCoverageTotalCalcResponse().getUnqueKey(), basicUiModel.getDisplayCurrency().name())
            || (Objects.isNull(basicUiModel.getDisplayCurrency()) || onlyBuyMtplAndPaGoods)
            && Objects.equals(x.getCoverageTotalCalcResponse().getUnqueKey(), basicUiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId()).getPlan().getPremiumCurrency().name());
    }

    private static void handleTotal(BatchQuotationResult.QuotationResult result) {
        if (!CollectionUtils.isEmpty(result.getGoodsItems())) {
            BatchQuotationResult.TotalResult totalResult = new BatchQuotationResult.TotalResult();
            BigDecimal totalAmount = result.getGoodsItems().values().stream()
                .map(item -> new BigDecimal(item.getCoverageTotalFinalPremium()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(result.getGoodsItems().values().stream().filter(item -> Objects.nonNull(item.getGreenCardFee()))
                .map(item -> new BigDecimal(item.getGreenCardFee()))
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            totalResult.setCoverageTotalFinalPremium(totalAmount.toPlainString());
            result.setTotal(totalResult);
        }
    }

    private void buildResult(String greenCardFee, BasicUiModel basicUiModel, List<CalcSaPremiumMultiPeriodResponse> sameCurrencyResponseList, BatchQuotationResult.QuotationResult basicCurrencyResult) {
        Map<String, BatchQuotationResult.GoodsResult> sameCurrencyResultMap = sameCurrencyResponseList.stream()
            .map(saPremiumMultiPeriodResponse -> buildGoodsResultMap(greenCardFee, basicUiModel, saPremiumMultiPeriodResponse)).flatMap(map -> map.entrySet().stream())
            .collect(Collectors.groupingBy(Map.Entry::getKey, Collectors.collectingAndThen(toList(), list -> list.get(0).getValue())));

        basicCurrencyResult.setGoodsItems(sameCurrencyResultMap);
    }

    private Map<String, BatchQuotationResult.GoodsResult> buildGoodsResultMap(String greenCardFee, BasicUiModel basicUiModel, CalcSaPremiumMultiPeriodResponse saPremiumMultiPeriodResponse) {
        Reflectors<AutoGoods> goodsReflectors =
            reflectorFactory.reflect(basicUiModel).reflect(AutoGoods.class);

        Reflectors<SaPremiumCalcResponse> grapheneReflectors =
            reflectorFactory.reflect(saPremiumMultiPeriodResponse).reflect(SaPremiumCalcResponse.class);

        var goodsMapping = goodsReflectors.zip(grapheneReflectors,
            portalReflector -> portalReflector.mapValueOpt(AutoGoods::getGoodsId).orElse(null),
            grapheneReflector -> grapheneReflector.mapValueOpt(SaPremiumCalcResponse::getGoodsId).orElse(null));

        return goodsMapping.entrySet().stream()
            .filter(goods -> Objects.nonNull(goods.getValue()))
            .filter(goods -> goods.getValue().valueOpt().isPresent())
            .collect(Collectors.toMap(
                good -> good.getKey().name(),
                good -> {
                    SaPremiumCalcResponse saPremiumCalcResponse = good.getValue().valueOpt().orElse(new SaPremiumCalcResponse());
                    BatchQuotationResult.GoodsResult goodsResult = new BatchQuotationResult.GoodsResult();
                    goodsResult.setGoodsId(saPremiumCalcResponse.getGoodsId());
                    goodsResult.setCurrency(EnumHelper.nameOf(saPremiumCalcResponse.getUnqueKey(), CurrencyEnum.class));
                    goodsResult.setCoverageTotalNetPremium(saPremiumCalcResponse.getPlanSaPremium().getCoverageTotalNetPremium());
                    goodsResult.setPeriodTotalTax(saPremiumCalcResponse.getPlanSaPremium().getPeriodTotalTax());
                    goodsResult.setCoverageTotalTax(saPremiumCalcResponse.getPlanSaPremium().getCoverageTotalTax());
                    if (Objects.nonNull(basicUiModel.getTrafficCompulsoryInsuranceGoods()) && Objects.equals(basicUiModel.getTrafficCompulsoryInsuranceGoods().getGoodsId(), saPremiumCalcResponse.getGoodsId())) {
                        goodsResult.setGreenCardFee(greenCardFee);
                    }
                    goodsResult.setCoverageTotalNoClaimDiscount(convertNoClaimDiscount(saPremiumCalcResponse.getPlanSaPremium().getCoverageTotalNoClaimDiscount()));
                    goodsResult.setCoverageTotalFinalPremium(saPremiumCalcResponse.getPlanSaPremium().getCoverageTotalFinalPremium());

                    if (!CollectionUtils.isEmpty(saPremiumMultiPeriodResponse.getPeriodList()) && saPremiumMultiPeriodResponse.getPeriodList().size() > 1) {
                        List<BatchQuotationResult.PeriodGoodsResult> periodGoodsResultList = saPremiumMultiPeriodResponse.getPeriodList().stream().map(period -> {
                            BatchQuotationResult.PeriodGoodsResult periodGoodsResult = new BatchQuotationResult.PeriodGoodsResult();
                            periodGoodsResult.setCurrency(EnumHelper.nameOf(saPremiumCalcResponse.getUnqueKey(), CurrencyEnum.class));
                            periodGoodsResult.setGoodsId(period.getGoodsId());
                            periodGoodsResult.setPeriodInInitialPeriod(period.getIsPeriodInInitialPeriod());
                            periodGoodsResult.setPeriodNo(period.getPeriodNo());
                            periodGoodsResult.setPeriodFinalPremium(period.getPlanSaPremium().getPeriodFinalPremium());
                            periodGoodsResult.setPeriodNetPremium(period.getPlanSaPremium().getPeriodNetPremium().toPlainString());
                            return periodGoodsResult;
                        }).toList();
                        goodsResult.setPeriodList(periodGoodsResultList);
                    }
                    final var campaignRelatingOutputs = buildCampaignResults(saPremiumCalcResponse);

                    goodsResult.setCampaigns(campaignRelatingOutputs);

                    Reflectors<SaPremiumCalcProductResponse> grapheneProductReflectors =
                        reflectorFactory.reflect(saPremiumCalcResponse)
                            .reflect(new TypeToken<List<SaPremiumCalcProductResponse>>() {
                            })
                            .flatten();

                    Reflectors<AutoProduct> productReflectors =
                        reflectorFactory.reflect(basicUiModel).reflect(AutoProducts.class).flatMap(pa -> pa.reflect(AutoProduct.class));

                    Reflectors<Liability> liabilityReflectors =
                        productReflectors
                            .flatMap(goodsReflector -> goodsReflector.reflect(AutoLiabilities.class)).flatMap(ya -> ya.reflect(Liability.class));

                    var mapping = productReflectors.zip(grapheneProductReflectors,
                        portalReflector -> portalReflector.mapValueOpt(AutoProduct::getProductId).orElse(null),
                        grapheneReflector -> grapheneReflector.mapValueOpt(SaPremiumCalcProductResponse::getProductId).orElse(null));

                    Map<String, BatchQuotationResult.ProductResult> productResultMap = buildProductResultMap(mapping, liabilityReflectors);
                    goodsResult.setProductItems(productResultMap);
                    return goodsResult;
                }
            ));
    }

    private Map<String, BatchQuotationResult.ProductResult> buildProductResultMap(Map<Reflector<AutoProduct>, Reflector<SaPremiumCalcProductResponse>> mapping, Reflectors<Liability> liabilityReflectors) {
        return mapping.entrySet().stream()
            .filter(a -> Objects.nonNull(a.getValue()))
            .filter(a -> a.getValue().valueOpt().isPresent())
            .collect(Collectors.toMap(
                a -> a.getKey().name(),
                a -> buildProductResult(liabilityReflectors, a)
            ));
    }

    private BatchQuotationResult.ProductResult buildProductResult(Reflectors<Liability> liabilityReflectors, Map.Entry<Reflector<AutoProduct>, Reflector<SaPremiumCalcProductResponse>> a) {
        BatchQuotationResult.ProductResult productResult = new BatchQuotationResult.ProductResult();
        SaPremiumCalcProductResponse sa = a.getValue().valueOpt().orElse(new SaPremiumCalcProductResponse());
        productResult.setProductId(sa.getProductId());
        productResult.setMainProduct(a.getKey().valueOpt().orElse(new AutoProduct()).getMainProduct());
        productResult.setSumInsured(sa.getProductSaPremium().getSumInsured());
        productResult.setCoverageTotalNoClaimDiscount(convertNoClaimDiscount(sa.getProductSaPremium().getCoverageTotalNoClaimDiscount()));
        productResult.setCoverageTotalNetPremium(sa.getProductSaPremium().getCoverageTotalNetPremium());
        productResult.setOriginalCoverageTotalNetPremium(sa.getProductSaPremium().getOriginalCoverageTotalNetPremium());
        productResult.setCoverageTotalDeltaNetPremium(sa.getProductSaPremium().getCoverageTotalDeltaNetPremium());
        productResult.setCoverageTotalStandardPremium(sa.getProductSaPremium().getCoverageTotalStandardPremium());
        Reflectors<SaPremiumLiabilityResponse> grapheneLiabilityReflectors =
            reflectorFactory.reflect(sa)
                .reflect(new TypeToken<List<SaPremiumLiabilityResponse>>() {
                })
                .flatten();

        var liabilityMapping = liabilityReflectors.zip(grapheneLiabilityReflectors,
            portalReflector -> portalReflector.mapValueOpt(Liability::getLiabilityId).orElse(null),
            grapheneReflector -> grapheneReflector.mapValueOpt(SaPremiumLiabilityResponse::getLiabilityId).orElse(null));

        Map<String, BatchQuotationResult.LiabilityResult> liabilityResultMap = liabilityMapping.entrySet().stream()
            .filter(l -> Objects.nonNull(l.getValue()))
            .filter(l -> l.getValue().valueOpt().isPresent())
            .collect(Collectors.toMap(
                l -> l.getKey().name(),
                this::buildLiabilityResult
            ));
        productResult.setLiabilityItems(liabilityResultMap);
        return productResult;
    }

    private List<BatchQuotationResult.CampaignResult> buildCampaignResults(SaPremiumCalcResponse saPremiumCalcResponse) {
        val campaignRelatingOutputs = ofNullable(saPremiumCalcResponse.getCampaignRelating())
            .map(e -> mapBy(ofNullable(e.getCampaigns()).orElseGet(Lists::newArrayList).stream().filter(ce -> Objects.nonNull(ce.getCampaignCode())).toList(), x -> {
                BatchQuotationResult.CampaignResult campaignResult = new BatchQuotationResult.CampaignResult();
                campaignResult.setCampaignCode(x.getCampaignCode());
                campaignResult.setCampaignCategory(x.getCampaignCategory());
                campaignResult.setOrderNo(x.getSortNo());
                ofNullable(x.getCateDiscount()).ifPresent(cate -> {
                    campaignResult.setDiscountType(cate.getDiscountType());
                    doIfPresent(cate.getPeriodTotalDiscount(), () -> campaignResult.setPeriodTotalDiscount(cate.getPeriodTotalDiscount().toPlainString()));
                    doIfPresent(cate.getCoverageTotalDiscount(), () -> campaignResult.setCoverageTotalDiscount(cate.getCoverageTotalDiscount().toPlainString()));
                    val payers = mapBy(cate.getPeriodTotalDiscountPayerList(), pay -> {
                        var payer = new BatchQuotationResult.Payer();
                        doIfPresent(pay.getDiscount(), () -> payer.setDiscount(pay.getDiscount().toPlainString()));
                        payer.setPayerCode(pay.getPayerCode());
                        doIfPresent(pay.getDiscountRate(), () -> payer.setDiscountRate(pay.getDiscountRate().toPlainString()));
                        payer.setPayerType(EnumHelper.codeOf(pay.getPayerType(), PayerTypeEnum.class));
                        payer.setPayerSubCode(pay.getPayerSubCode());
                        return payer;
                    });
                    campaignResult.setPayers(payers);
                });
                return campaignResult;
            })).orElse(Lists.newArrayList());
        return campaignRelatingOutputs;
    }

    private BatchQuotationResult.LiabilityResult buildLiabilityResult(Map.Entry<Reflector<Liability>, Reflector<SaPremiumLiabilityResponse>> l) {
        SaPremiumLiabilityResponse saPremiumLiabilityResponse = l.getValue().valueOpt().orElse(new SaPremiumLiabilityResponse());
        BatchQuotationResult.LiabilityResult liabilityResult = new BatchQuotationResult.LiabilityResult();
        liabilityResult.setLiabilityId(saPremiumLiabilityResponse.getLiabilityId());
        liabilityResult.setSumInsured(saPremiumLiabilityResponse.getLiabilitySaPremium().getSumInsured());
        liabilityResult.setPremiumDiscountList(buildLiabilityDiscountList(l.getKey().name(), saPremiumLiabilityResponse.getLiabilitySaPremium().getDiscountPremiumDetailList()));
        liabilityResult.setSurchargeList(buildLiabilityChargeList(l.getKey().name(), saPremiumLiabilityResponse.getLiabilitySaPremium().getExtraPremiumDetailList()));
        return liabilityResult;
    }

    private List<BatchQuotationResult.SurchargeResult> buildLiabilityChargeList(String liabilityFiledName, List<LiabilityExtraPremiumDetailResponse> extraPremiumDetailList) {
        return ofNullable(extraPremiumDetailList)
            .map(list -> list.stream()
                .map(x -> mapToPremiumChargeResult(liabilityFiledName, x))
                .collect(toList()))
            .orElse(Collections.emptyList());
    }

    private BatchQuotationResult.SurchargeResult mapToPremiumChargeResult(String liabilityFiledName, LiabilityExtraPremiumDetailResponse liabilityChargeResponse) {
        BatchQuotationResult.SurchargeResult surchargeResult = new BatchQuotationResult.SurchargeResult();
        surchargeResult.setExtraLoadingType(EnumHelper.codeOf(liabilityChargeResponse.getLoadingType(), AdditionalTypeEnum.class));
        surchargeResult.setLoadingMethod(EnumHelper.codeOf(liabilityChargeResponse.getLoadingMethod(), LoadingMethodEnum.class));
        surchargeResult.setCalculationBasis(EnumHelper.codeOf(liabilityChargeResponse.getCalculationBasis(), NcdCalculationBasisEnum.class));
        surchargeResult.setOrderNo(liabilityChargeResponse.getOrderNo());
        surchargeResult.setRate(liabilityChargeResponse.getRate());
        surchargeResult.setAmount(liabilityChargeResponse.getCoverageTotalExtraPremium());
        surchargeResult.setLiabilityFiledName(liabilityFiledName);
        return surchargeResult;
    }

    private List<BatchQuotationResult.PremiumDiscountResult> buildLiabilityDiscountList(String liabilityFiledName, List<ProductDiscountPremiumDetailResponse> discountPremiumDetailList) {
        return ofNullable(discountPremiumDetailList)
            .map(list -> list.stream()
                .map(x -> mapToPremiumDiscountResult(liabilityFiledName, x))
                .collect(toList()))
            .orElse(Collections.emptyList());
    }

    private BatchQuotationResult.PremiumDiscountResult mapToPremiumDiscountResult(String liabilityFiledName, ProductDiscountPremiumDetailResponse discountPremiumDetail) {
        BatchQuotationResult.PremiumDiscountResult premiumDiscountResult = new BatchQuotationResult.PremiumDiscountResult();
        premiumDiscountResult.setPremiumDiscountType(EnumHelper.codeOf(discountPremiumDetail.getPremiumDiscountType(), PremiumDiscountTypeEnum.class));
        premiumDiscountResult.setRate(discountPremiumDetail.getRate());
        premiumDiscountResult.setCoverageTotalPremiumDiscount(discountPremiumDetail.getCoverageTotalPremiumDiscount());
        premiumDiscountResult.setOrderNo(discountPremiumDetail.getOrderNo());
        premiumDiscountResult.setLiabilityFiledName(liabilityFiledName);
        return premiumDiscountResult;
    }

    private static String convertNoClaimDiscount(String noClaimDiscountAmount) {
        if (Objects.isNull(noClaimDiscountAmount)) {
            return null;
        }
        if (Objects.equals(BigDecimal.ZERO, new BigDecimal(noClaimDiscountAmount))) {
            return BigDecimal.ZERO.toPlainString();
        }
        return new BigDecimal(noClaimDiscountAmount).negate().toString();
    }

}
