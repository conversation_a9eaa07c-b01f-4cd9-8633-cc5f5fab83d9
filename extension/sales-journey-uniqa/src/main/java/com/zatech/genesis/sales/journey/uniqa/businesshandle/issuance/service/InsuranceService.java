/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.IssuanceCreateResponse;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderCreateService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderQueryService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderUpdateService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.CreateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartOrderStatusEnum;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.copy.OnDemandCopier;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.issuance.IssuanceService;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.IssuanceBusinessResult;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Customer;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Individual;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.integration.market.MarketAdapter;
import com.zatech.genesis.sales.journey.integration.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.sales.journey.order.api.PolicyRecordManager;
import com.zatech.genesis.sales.journey.order.api.errorcode.PolicyRecordErrorCode;
import com.zatech.genesis.sales.journey.order.api.input.UpdatePolicyRecordInput;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.payment.api.IssueWithoutPaymentManager;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.IssuanceCheckService;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.filler.IssuanceRequestFillerFactory;
import com.zatech.genesis.sales.journey.uniqa.lifecycle.order.TcVirtualBranchProcessor;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;
import com.zatech.genesis.sales.journey.uniqa.uimodel.wrapper.SelectGoodsWrapper;
import com.zatech.octopus.common.util.AssertUtil;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import jakarta.validation.constraints.NotNull;

import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE;
import static com.zatech.genesis.sales.journey.order.api.errorcode.PolicyRecordErrorCode.ISSUANCE_STATUS_IS_ILLEGAL;

@Slf4j
@Component
public class InsuranceService {

    private static final String COMPLIANCE_TASK_DECLINE = "decline";

    @Autowired
    private PolicyRecordManager policyRecordManager;

    @Autowired
    private UIModelOrderQueryService uiModelOrderQueryService;

    @Autowired
    private UIModelOrderCreateService uiModelOrderCreateService;

    @Autowired
    private UIModelOrderUpdateService uiModelOrderUpdateService;

    @Autowired
    private Converters converters;

    @Autowired
    private IssuanceService issuanceService;

    @Autowired
    private IssueWithoutPaymentManager issueWithoutPaymentManager;

    @Autowired
    private IssuanceCheckService issuanceCheckService;

    @Autowired
    private IssuanceRequestFillerFactory issuanceRequestFillerFactory;

    @Autowired
    private MarketAdapter marketAdapter;

    @Autowired
    private CreateProposalCheckService createProposalCheckService;

    @Transactional
    public IssuanceResult handle(IPhase phase, BasicUiModel uiModel, BusinessHandleContext context) {
        OrderContext order = context.getOrder();
        log.info("Phase: {} Order: {} start issuance", phase, order.getOrderNo());

        ContextHolder contextHolder = new ContextHolder(context);

        issuanceCheckService.checkIfLockByUser(contextHolder);

        List<Long> goodsIdList = new SelectGoodsWrapper(uiModel, context, createProposalCheckService).issuanceSelectedGoodsId();

        if (CollectionUtils.isEmpty(goodsIdList)) {
            return IssuanceResult.builder().createResponses(Collections.emptyList()).build();
        }

        //需要创建投保单的orderIds
        List<Long> orderIds = contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(e -> goodsIdList.contains(e.getGoodsId()))
            .map(PolicyRecordInfoOutput::getOrderId)
            .toList();

        checkStatus(uiModel, contextHolder, orderIds);

        setDefaultGendersForHolderAndInsured(uiModel);

        createUiModel(uiModel, contextHolder);

        List<IssuanceRequest> requestList = buildIssuanceReqestList(phase, uiModel, contextHolder, orderIds);

        issuanceRequestFillerFactory.getIssuanceRequestFiller(uiModel.getClass()).fillRequest(requestList, uiModel, contextHolder, context);

        IssuanceResult result = batchCreate(requestList, contextHolder.getTransactionOrderNo().orElse(null));

        updatePolicyRecord(contextHolder, result);

        //更改订单状态为submitted
        orderSubmitted(contextHolder, result);

        requestList
            .forEach(
                e -> {
                    OrderContextHolder orderContextHolder = contextHolder.queryByOrderNo(e.getBizApplyNo());
                    if (canIssue(orderContextHolder.getIssuance().orElse(null), orderContextHolder.getOrderContext().getTransactionOrderContextOpt().orElse(orderContextHolder.getOrderContext()))) {
                        issueWithoutPaymentManager.createIssueWithoutPayment(orderContextHolder.getOrderId());
                    }
                });
        return result;
    }

    private void setDefaultGendersForHolderAndInsured(BasicUiModel uiModel) {
        Optional.ofNullable(uiModel.getPolicyHolder())
            .map(Customer::getIndividual).ifPresent(this::setDefaultGenderIfNull);

        Optional.ofNullable(uiModel.getInsured())
            .map(Customer::getIndividual).ifPresent(this::setDefaultGenderIfNull);
    }

    private void setDefaultGenderIfNull(Individual individual) {
        GenderEnum gender = individual.getGender();
        if (gender == null) {
            individual.setGender(GenderEnum.UNKNOWN);
        }
    }

    /**
     * pa combine journey 需要在mtpl后出单
     *
     * @param issuance
     * @param order
     * @return
     */
    private boolean canIssue(IssuanceResponse issuance, OrderContext order) {
        AssertUtil.notNull(issuance);
        String goodsCode = issuance.getGoodsCode();

        if (!Objects.equals(goodsCode, GoodsCodeEnum.PA.getGoodsCode())) {
            return true; // 直接返回，非 PA 商品情况
        }

        log.info("Check if PA is with MTPL");

        for (OrderContext subOrderContext : order.getSubOrderContexts()) {
            OrderContextHolder contextHolder = new OrderContextHolder(subOrderContext);

            if (existMTPLWaitingIssuance(contextHolder)) {
                return false;
            }

            Optional<IssuanceResponse> mtplIssuance = contextHolder.getIssuance()
                .filter(issuanceResponse -> Objects.equals(issuanceResponse.getGoodsCode(), GoodsCodeEnum.MTPL.getGoodsCode()));
            //存在投保单为mtpl的，需要判断是否effective
            if (mtplIssuance.isPresent()) {
                return mtplIssuance.get().getIssuanceStatus() == IssuanceStatusEnum.EFFECTIVE;
            }
        }

        return true;
    }

    /**
     * 是否存在暂存单，为MTPL，存在Pa就不能出，返回false
     *
     * @param contextHolder
     * @return
     */
    private boolean existMTPLWaitingIssuance(OrderContextHolder contextHolder) {
        return contextHolder.getWaitingForIssuance()
            .filter(issuanceResponse -> issuanceResponse.getIssuanceStatus() == IssuanceStatusEnum.DATA_ENTRY_IN_PROGRESS)
            .map(issuanceResponse ->
                Optional.ofNullable(issuanceResponse.getGoodsCode())
                    .orElseGet(() -> marketAdapter.queryGoodsBasicOpt(issuanceResponse.getGoodsId())
                    .map(GoodsBasicInfoResp::getGoodsCode)
                    .orElse(null)))
            .map(GoodsCodeEnum.MTPL.getGoodsCode()::equals)
            .orElse(false);
    }

    protected IssuanceResult batchCreate(List<IssuanceRequest> requests, String transactionOrderNo) {
        requests.forEach(e -> {
            if (!StringUtils.equals(e.getBranchCode(), TcVirtualBranchProcessor.VIRTUAL_BRANCH_CODE)) {
                AssertUtil.notNull(e.getSalesAgreementCode(), "salesAgreementCode must not be null");
            }
        });
        return issuanceService.batchCreate(requests, transactionOrderNo);
    }

    private void updatePolicyRecord(ContextHolder contextHolder, IssuanceResult result) {
        if (CollectionUtils.isEmpty(result.getCreateResponses())) {
            return;
        }
        result.getCreateResponses().stream().map(d -> {
            Long orderId = contextHolder.queryByOrderNo(d.getBizApplyNo()).getOrderId();
            UpdatePolicyRecordInput policyRecord = new UpdatePolicyRecordInput();
            policyRecord.setPolicyNo(d.getPolicyNo());
            policyRecord.setIssuanceTransactionType(UW_AND_CREATEISSUANCE);
            policyRecord.setIssuanceNo(d.getIssuanceNo());
            policyRecord.setOrderId(orderId);
            return policyRecord;
        }).forEach(policyRecordManager::update);

    }

    @NotNull
    private List<IssuanceRequest> buildIssuanceReqestList(IPhase phase, BasicUiModel uiModel, ContextHolder contextHolder, List<Long> orderIds) {
        List<IssuanceRequest> issuanceRequests = orderIds.stream()
            .map(contextHolder::queryByOrderId)
            .filter(Objects::nonNull)
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(e -> {
                OrderContext orderContext = contextHolder.queryByOrderId(e.getOrderId()).getOrderContext();
                AutoGoods autoGoods = uiModel.selectedGoods(e.getGoodsId());
                BusinessHandleContext businessHandleContext = new BusinessHandleContext(orderContext, TraceOp.getTenant());
                contextHolder.getUserInfoHolder().ifPresent(x -> businessHandleContext.setUserInfo(x.getUserInfo()));
                businessHandleContext.setTrailContext(contextHolder.getTrailContext().orElse(null));
                AutoConvertIssuanceRequestContext issuanceRequestContext = new AutoConvertIssuanceRequestContext(businessHandleContext);
                issuanceRequestContext.setSelectedGoodsId(autoGoods.getGoodsId());
                issuanceRequestContext.setSelectedGoods(autoGoods);
                issuanceRequestContext.setPhase(phase);
                IssuanceRequest issuanceRequest = new IssuanceRequest();
                issuanceRequest = converters.convert(uiModel, issuanceRequest, issuanceRequestContext);
                issuanceRequest.setIssuanceNo(e.getIssuanceNo());
                // only mtpl set policyNo
                if (Objects.equals(autoGoods.getGoodsCode(), GoodsCodeEnum.MTPL.getGoodsCode())) {
                    issuanceRequest.setPolicyNo(uiModel.getPolicyNoFromUosNbo());
                }
                issuanceRequest.setIssuanceTransactionType(UW_AND_CREATEISSUANCE);
                return issuanceRequest;
            }).toList();
        return issuanceRequests.stream().sorted(Comparator.comparing(e -> Optional.ofNullable(GoodsCodeEnum.queryByGoodsCode(e.getGoodsCode())).map(GoodsCodeEnum::getSort).orElse(Integer.MAX_VALUE))).toList();
    }

    private void createUiModel(BasicUiModel uiModel, ContextHolder contextHolder) {
        contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord).filter(Optional::isPresent).map(Optional::get)
            //过滤已经有uiModel的情况
            .filter(policyRecord -> !existUiModel(policyRecord.getOrderId()))
            .forEach(policyRecord -> {
                var orderId = policyRecord.getOrderId();
                var goodsId = policyRecord.getGoodsId();
                var copiedUiModel = OnDemandCopier.copy(uiModel, null);
                //拆单的时候需要把每个单子上不输入该goods的rider给删除掉，不然会影响拆单以后使用子order操作的时候rider被带进去的情况
                copiedUiModel.retainRiderProductBy(goodsId);
                copiedUiModel.retainFieldsBy(goodsId);
                var request = new CreateUIModelInput<>(copiedUiModel, new JsonMap(), false);
                uiModelOrderCreateService.createUIModel(orderId, request);
            });
    }

    private void orderSubmitted(ContextHolder contextHolder, IssuanceResult result) {
        if (CollectionUtils.isEmpty(result.getCreateResponses())) {
            return;
        }
        result.getCreateResponses().stream()
            .filter(e -> e.getIssuanceStatus() == IssuanceStatusEnum.WAITING_FOR_ISSUANCE)
            //bizApplyNo是subOrderNo
            .map(IssuanceCreateResponse::getBizApplyNo)
            .map(uiModelOrderQueryService::queryOrThrow)
            .map(UIModelOrderOutput::getOrderId)
            .forEach(orderId -> uiModelOrderUpdateService.updateOrderStatus(orderId, FreeMartOrderStatusEnum.SUBMITTED));
    }

    private void checkStatus(BasicUiModel uiModel, ContextHolder contextHolder, List<Long> orderIds) {
        for (Long orderId : orderIds) {
            UIModelOrderOutput<IUIModel> orderOutput = uiModelOrderQueryService.queryOrThrow(orderId);
            if (FreeMartOrderStatusEnum.UNFINISHED != orderOutput.getStatus()) {
                log.info(" Order: {} start issuance", orderOutput.getOrderNo());
                throw CommonException.byErrorAndParams(PolicyRecordErrorCode.ORDER_CONFIRM_JUDGE_ORDER_STATUS_NOT_UNFINISHED, orderOutput.getOrderNo());
            }
        }
        if (contextHolder.getOrderContextHolders().stream().anyMatch(OrderContextHolder::existCompliance) && uiModel.getAllGoods().stream().map(AutoGoods::getElements).noneMatch(elements -> Objects.equals(COMPLIANCE_TASK_DECLINE, elements.getComplianceTask()))) {
            log.error("Order exist open or decline compliance, so can`t issue. orderIds:{}", orderIds);
            throw CommonException.byError(ISSUANCE_STATUS_IS_ILLEGAL);
        }

        for (Long orderId : orderIds) {
            contextHolder.queryByOrderId(orderId).getIssuanceNo().ifPresent(e -> issuanceCheckService.checkStatus(e));
        }

    }

    private boolean existUiModel(Long orderId) {
        return uiModelOrderQueryService.queryOpt(orderId).flatMap(UIModelOrderOutput::getDataOpt).isPresent();
    }

    public Result onStop(IssuanceResult issuanceResult) {
        return IssuanceBusinessResult.builder()
            .data(Optional.ofNullable(issuanceResult).map(IssuanceResult::getCreateResponses).orElse(Collections.emptyList()).stream().map(IssuanceBusinessResult::map).toList())
            .build();
    }

}
