/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.filler;

import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:37
 **/
@Component
public class IssuanceRequestFillerFactory {

    private Map<Class<? extends BasicUiModel>, IIssuanceRequestFiller> fillerMap;

    @Autowired
    private IssuanceRequestFillerFactory (List<IIssuanceRequestFiller> fillers) {
        fillerMap = fillers.stream().collect(Collectors.toMap(IIssuanceRequestFiller::type, Function.identity()));
    }

    public IIssuanceRequestFiller getIssuanceRequestFiller(Class<? extends BasicUiModel> clazz) {
        return fillerMap.get(clazz);
    }
}
