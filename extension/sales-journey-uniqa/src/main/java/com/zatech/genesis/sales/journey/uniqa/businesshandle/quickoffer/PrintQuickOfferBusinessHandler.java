/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Customer;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Individual;
import com.zatech.genesis.sales.journey.integration.notification.AsyncSendNotificationHandler;
import com.zatech.genesis.sales.journey.integration.notification.NotificationCommand;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.share.dto.notification.send.BusinessNoTuple;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer.mapper.QuickOfferMapper;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer.result.QuickOfferResult;
import com.zatech.genesis.sales.journey.uniqa.processor.quickoffer.param.PrintQuickOfferParam;
import com.zatech.genesis.sales.journey.uniqa.processor.quickoffer.result.PrintQuickOfferResult;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.common.CommonConvertContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import static com.zatech.gaia.resource.notification.NotifyBusinessTypeEnum.PROPOSAL;
import static java.util.Optional.ofNullable;

/**
 * @Author: weizhen.kong
 */
@Component
@AllArgsConstructor
@UIModelBusinessHandler(name = "printQuickOffer", kind = BusinessHandlerKind.market, desc = "Print quick offer")
public class PrintQuickOfferBusinessHandler implements IAuthBusinessHandler<MarketPhase, BasicUiModel, PrintQuickOfferParam> {

    private final AsyncSendNotificationHandler<NotificationCommand> asyncSendNotificationHandler;

    private final Converters converters;

    @Override
    public MarketPhase[] supportedPhases() {
        return new MarketPhase[]{MarketPhase.printQuickOffer};
    }

    @Override
    public Result onContinueError(Exception e, MarketPhase phase, BasicUiModel businessModel, PrintQuickOfferParam param,
                                  BusinessHandleContext context) {
        throw CommonException.byErrorAndCause(CommonBusinessHandleErrorCodes.PRINT_QUICK_OFFER_FAIL, e);
    }

    @Override
    public Result onContinueSucceed(Result continueResult, MarketPhase phase, BasicUiModel businessModel, PrintQuickOfferParam param,
                                    BusinessHandleContext context) {
        PrintQuickOfferResult quickOfferResult = (PrintQuickOfferResult) continueResult;
        QuickOfferNotificationCommand quickOfferNotificationCommand = new QuickOfferNotificationCommand(quickOfferResult.getAttachment(),
                new ContextHolder(context), quickOfferResult.getSimpleFactors());

        new ContextHolder(context)
                .getOrderContextHolders()
                .stream()
                .map(OrderContextHolder::getPolicyRecord)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(policyRecord -> Objects.nonNull(policyRecord.getIssuanceNo()))
                .map(PolicyRecordInfoOutput::getIssuanceNo)
                .findFirst()
                .ifPresent(proposalNo ->
                        quickOfferNotificationCommand.setTriggerBusinessNo(new BusinessNoTuple(PROPOSAL, proposalNo)));
        asyncSendNotificationHandler.asyncSendNotification(quickOfferNotificationCommand);
        return new QuickOfferResult().setFileUniqueCode(quickOfferResult.getDownloadFileUniqueCode());
    }

    @Override
    public FlowStrategy handle(MarketPhase phase, BasicUiModel businessModel, PrintQuickOfferParam param, BusinessHandleContext context) {
        //mtpl need print bm
        AutoGoods trafficCompulsoryInsuranceGoods = businessModel.getTrafficCompulsoryInsuranceGoods();
        if (Objects.nonNull(trafficCompulsoryInsuranceGoods) && Objects.equals(true, trafficCompulsoryInsuranceGoods.getSelected())) {
            CommonConvertContext commonConvertContext = new CommonConvertContext();
            commonConvertContext.setOrderContext(context.getOrder());
            commonConvertContext.setTrailContextOpt(context.getTrailOpt());
            commonConvertContext.setPhase(phase);
            commonConvertContext.setSelectedGoodsId(trafficCompulsoryInsuranceGoods.getGoodsId());
            commonConvertContext.setSelectedGoods(trafficCompulsoryInsuranceGoods);
            businessModel = converters.convert(businessModel, new BasicUiModel(), commonConvertContext);
        }
        param.setQuickQuotation(QuickOfferMapper.INSTANCE.convert(businessModel.getQuickQuotation()));
        param.setVehicleInfo(QuickOfferMapper.INSTANCE.convert(businessModel.getVehicleInfo()));
        param.setAgentCode(context.getOrder().getExtension().getAgentCode());
        param.setBranchCode(context.getOrder().getExtension().getBranchCode());
        List<AutoGoods> list = new ArrayList<>();
        for (Long goodsId : businessModel.selectedGoodsId()) {
            if (!goodsId.equals(ofNullable(businessModel.getPassengerAccidentGoods()).map(AutoGoods::getGoodsId).orElse(-1L))) {
                AutoGoods autoGoods = businessModel.selectedGoods(goodsId);
                list.add(autoGoods);
            }
        }
        param.setGoods(list);
        param.setBirthday(ofNullable(businessModel.getInsured())
                .filter(insured -> ObjectUtils.isNotEmpty(insured.getIndividual()))
                .map(Customer::getIndividual)
                .map(Individual::getBirthday)
                .orElse(null));
        param.setOrderNo(context.getOrder().getOrderNo());
        ContextHolder contextHolder = new ContextHolder(context);
        List<PrintQuickOfferParam.OrderContextParam> quickOfferCaseParam = contextHolder.getOrderContextHolders()
                .stream()
                .filter(orderContextHolder -> orderContextHolder.getPolicyRecord().isPresent())
                .map(orderContextHolder -> {
                    PolicyRecordInfoOutput policyRecord = orderContextHolder.getPolicyRecord().orElse(null);
                    PrintQuickOfferParam.OrderContextParam printEofferCaseParam = new PrintQuickOfferParam.OrderContextParam();
                    printEofferCaseParam.setGoodsId(policyRecord.getGoodsId());
                    printEofferCaseParam.setOrderNo(orderContextHolder.getOrderNo());
                    printEofferCaseParam.setIssuanceNo(policyRecord.getIssuanceNo());
                    printEofferCaseParam.setPolicyNo(policyRecord.getPolicyNo());
                    return printEofferCaseParam;
                }).toList();
        param.setOrderCases(quickOfferCaseParam);
        return FlowStrategy.Continue;
    }

}