/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.file;

import com.za.cqrs.util.Functions;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.gaia.resource.components.enums.issuance.UnderwritingStatusEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.NBConfigurationTypeEnum;
import com.zatech.genesis.policy.api.base.IssuanceBase;
import com.zatech.genesis.policy.api.response.IssuanceRelationDetailResponse;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.IssuanceRuleResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Attachment;
import com.zatech.genesis.sales.journey.integration.notification.IOuterPendingCaseService;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.command.SyncAttachmentCommandContainer;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.command.SyncAttachmentInput;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.manager.FileManager;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.param.SyncFileParam;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.result.SyncFileResult;
import com.zatech.genesis.sales.journey.uniqa.errorcode.FileProcessorErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import static com.zatech.genesis.sales.journey.uniqa.errorcode.FileProcessorErrorCode.ISSUANCE_NOT_FOUND;
import static java.util.Optional.ofNullable;

/**
 * <AUTHOR>
 * @date 2024/5/10 10:58
 **/
@Slf4j
public abstract class AbstractSyncFileBusinessHandler implements IAuthBusinessHandler<PolicyPhase, SyncFileParam, BasicUiModel> {

    @Autowired
    private IOuterPendingCaseService pendingCaseService;

    @Autowired
    private IOuterPolicyService policyService;

    @Autowired
    private FileManager fileManager;

    private final static List<String> MANUAL_STATUS = List.of(UnderwritingStatusEnum.PENDING_MANUAL_UW.getCode(),
        UnderwritingStatusEnum.ACCEPTED_MANUAL.getCode(), UnderwritingStatusEnum.DECLINED_MANUAL.getCode(), UnderwritingStatusEnum.POSTPONED_MANUAL.getCode());


    @Override
    public PolicyPhase[] supportedPhases() {
        return new PolicyPhase[]{PolicyPhase.syncFile};
    }

    @Override
    public Result onContinueError(Exception e, PolicyPhase phase, SyncFileParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        return FailureResult.error(e.getMessage());
    }

    @Override
    public Result onContinueSucceed(Result result, PolicyPhase phase, SyncFileParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        return result;
    }

    @Override
    public Result onStop(PolicyPhase phase, SyncFileParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        return SyncFileResult.builder().operateResult(true).build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FlowStrategy handle(PolicyPhase phase, SyncFileParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        if (CollectionUtils.isEmpty(param.getAttachments())) {
            log.warn("Request attachments is empty");
            return FlowStrategy.Stop;
        }
        //再校验下，避免前端传了空对象
        if (param.getAttachments().stream().anyMatch(attachment -> StringUtils.isBlank(attachment.getAttachmentUrl()))) {
            throw CommonException.byError(FileProcessorErrorCode.ATTACHMENT_IS_MISSING);
        }
        log.info("Request param :{}", StaticJsonParser.toJsonString(param));
        Optional<OrderContext> transactionOrderContext = context.getOrder().getTransactionOrderContextOpt();
        List<PolicyRecordInfoOutput> records;

        // 说明是子订单号
        if (transactionOrderContext.isPresent()) {
            log.info("Current order is sub order");
            OrderContextHolder orderContextHolder = new OrderContextHolder(context.getOrder());
            // 暂存单忽略
            if (orderContextHolder.getPolicyRecord().map(PolicyRecordInfoOutput::isTempInsurance).orElse(false)) {
                log.warn("this order record is tempInsurance");
                return FlowStrategy.Stop;
            }

            IssuanceResponse issuanceResponse = orderContextHolder.getIssuance()
                .orElseThrow(() -> CommonException.byError(ISSUANCE_NOT_FOUND));
            String relationNo = getRelationNo(issuanceResponse);

            PolicyRecordInfoOutput subOrderRecord = orderContextHolder.getPolicyRecord().orElseThrow(() -> CommonException.byError(FileProcessorErrorCode.POLICY_RECORD_NOT_FOUND));

            if (relationNo == null) {
                // 没有 relationNo，说明已经拆单，只同步自己的
                records = List.of(subOrderRecord);
            } else {
                ContextHolder contextHolder = new ContextHolder(transactionOrderContext.orElse(null));
                List<String> issuanceNos = getIssuanceNos(contextHolder, relationNo);
                //必有一条本身的数据
                Functions.doIf(issuanceNos.size() < 2, () -> log.info("May be one record has be manual"));
                records = issuanceNos.size() < 2 ? List.of(subOrderRecord) : getRecords(contextHolder, issuanceNos);
            }
        } else {
            ContextHolder contextHolder = new ContextHolder(context);
            records = getRecords(contextHolder, null);
        }

        //针对copy的订单和老订单的处理
        List<Attachment> attachments = param.getAttachments().stream().filter(data -> Boolean.TRUE != data.getNeedDelete()).toList();
        fileManager.resetOrSaveFrontContext(records, attachments, context);

        doExecute(new SyncAttachmentCommandContainer(build(records, param), policyService));

        return FlowStrategy.Stop;
    }

    /**
     * 获取关系单
     *
     * @param issuanceResponse
     * @return
     */
    protected String getRelationNo(IssuanceResponse issuanceResponse) {
        return Optional.ofNullable(issuanceResponse)
            .map(IssuanceResponse::getIssuanceRelationDetailList)
            .flatMap(list -> list.stream().findFirst())
            .map(IssuanceRelationDetailResponse::getRelationPolicyNo)
            .orElse(null);
    }

    protected List<String> getIssuanceNos(ContextHolder contextHolder, String relationNo) {
        List<IssuanceResponse> issuanceResponses = contextHolder.getOrderContextHolders().stream()
            .map(OrderContextHolder::getIssuance)
            .filter(Optional::isPresent)
            .map(Optional::get)
            //如果有人核记录的，就不要互通了
            .filter(res -> !hasManualRecord(res))
            .toList();
        return issuanceResponses.stream()
            .filter(data -> data.getIssuanceRelationDetailList().stream()
                .anyMatch(relation -> relationNo.equals(relation.getRelationPolicyNo())))
            .map(IssuanceResponse::getIssuanceNo)
            .toList();
    }

    /**
     * 是否有人核记录
     *
     * @return
     */
    protected boolean hasManualRecord(IssuanceResponse response) {
        List<IssuanceRuleResponse> issuanceRuleResponses = policyService.queryIssuanceRules(response.getIssuanceNo(), null, null);
        log.info("Rule decision : {}", StaticJsonParser.toJsonString(issuanceRuleResponses));
        return Optional.ofNullable(issuanceRuleResponses)
            .orElse(Collections.emptyList())
            .stream()
            .anyMatch(d -> NBConfigurationTypeEnum.PROPOSAL_UNDERWRITING == d.getType() && MANUAL_STATUS.contains(d.getStatus()));
    }

    /**
     * issuanceNos不为空，说明页面进入的是使用子订单，且拥有相同relationNo的数据
     *
     * @param contextHolder
     * @param issuanceNos
     * @return
     */
    private List<PolicyRecordInfoOutput> getRecords(ContextHolder contextHolder, List<String> issuanceNos) {
        log.info("GetRecords request issuanceNos : {}", StaticJsonParser.toJsonString(issuanceNos));
        return contextHolder.getOrderContextHolders().stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(record -> issuanceNos == null || issuanceNos.contains(record.getIssuanceNo()))
            .filter(record -> IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE != record.getIssuanceTransactionType())
            .toList();
    }

    public void doExecute(SyncAttachmentCommandContainer container) {
        if (CollectionUtils.isNotEmpty(container.pendingCaseAttachmentCommands())) {
            var request = container.pendingCaseAttachmentCommands().get(0);
            request.pendingCaseAttachment().setIsClose(Boolean.TRUE);
            log.info("Synchronize attachments to the pending case, pending case id:{}, request:{}", request.pendingCaseId(), StaticJsonParser.toJsonString(request));
            pendingCaseService.updatePendingCaseFileAndClosed(request.pendingCaseId(), request.pendingCaseAttachment());
        }

        ofNullable(container.issuanceCommands())
            .orElse(Collections.emptyList())
            .forEach(command -> {
                var request = command.issuanceAttachment();
                log.info("Synchronize attachments to the issuance, issuanceNo:{}, request:{}", command.issuanceNo(), StaticJsonParser.toJsonString(request));
                policyService.uploadIssuancesAttachments(command.issuanceNo(), request);
            });

        ofNullable(container.policyAttachmentCommands())
            .orElse(Collections.emptyList())
            .forEach(command -> {
                var request = command.policyAttachment();
                log.info("Synchronize attachments to the policy, policyNo:{}, request:{}", command.policyNo(), StaticJsonParser.toJsonString(request));
                policyService.uploadPoliciesAttachments(command.policyNo(), request);
            });
    }

    public List<SyncAttachmentInput> build(List<PolicyRecordInfoOutput> records, SyncFileParam param) {
        return records.stream().map(record -> {
            IssuanceStatusEnum status = ofNullable(policyService.getIssuance(record.getIssuanceNo(), null))
                .map(IssuanceBase::getIssuanceStatus).orElseThrow(() -> CommonException.byError(ISSUANCE_NOT_FOUND));
            SyncAttachmentInput input = new SyncAttachmentInput();
            log.info("Policy status is : {}", status);
            if (status == IssuanceStatusEnum.EFFECTIVE) {
                input.setPolicyNo(record.getPolicyNo());
                input.setIssuanceNo(record.getIssuanceNo());
            } else {
                input.setIssuanceNo(record.getIssuanceNo());
            }
            if (StringUtils.isNotBlank(param.getPendingCaseNo())) {
                input.setPendingCaseNo(param.getPendingCaseNo());
                input.setPendingCaseId(pendingCaseService.pendingCaseFindByNo(param.getPendingCaseNo()).getId());
            }
            input.setAttachments(param.getAttachments());
            return input;
        }).toList();
    }

}
