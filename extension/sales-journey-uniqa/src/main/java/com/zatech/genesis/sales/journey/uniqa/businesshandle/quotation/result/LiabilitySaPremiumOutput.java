/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result;

import java.util.List;
import java.util.Map;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class LiabilitySaPremiumOutput {

    /**
     * 责任Id
     */
    private Long liabilityId;

    /**
     * 联合定价同组的责任
     */
    private List<Long> liabilityIds;

    /**
     * 分组
     */
    private Integer groupId;

    /**
     * 责任名称
     */
    private String liabilityName;

    /**
     * 责任别名
     */
    private String liabilityRemark;

    /**
     * 期缴-标准保费（不含税）
     */
    private String periodStandardPremium;

    /**
     * 期缴-标准保费（含税）
     */
    private String periodStandardPremiumIncTax;

    /**
     * 期缴-加费（不含税）
     */
    private String periodExtraPremium;

    /**
     * 期缴-加费（含税）
     */
    private String periodExtraPremiumIncTax;

    /**
     * 期缴-税合计
     */
    private String periodTotalTax;

    /**
     * 基准保额
     */
    private String sumInsured;

    /**
     * 营销活动赠送保额
     */
    private String campaignFreeSumInsured;

    /**
     * 最终保额
     */
    private String finalSumInsured;

    /**
     * periodNoClaimDiscount
     */
    private String periodNoClaimDiscount;

    /**
     * periodNoClaimDiscountIncTax
     */
    private String periodNoClaimDiscountIncTax;

    private String annualNoClaimDiscount;

    private String annualNoClaimDiscountIncTax;

    /**
     * 仅在 calculationBasis = Rate 时有返回值
     */
    private String noClaimDiscountRate;

    /**
     * 期缴-险种折扣保费
     */
    private String periodDiscountPremium;

    /**
     * 期缴-险种折扣保费
     */
    private String periodDiscountPremiumIncTax;

    /**
     * 分期-短期费率
     */
    private String shortRate;

    /**
     * the standard premium of coverage total
     */
    private String coverageTotalStandardPremium;

    /**
     * the net premium of coverage total
     */
    private String coverageTotalNetPremium;

    /**
     * the total tax of coverage total
     */
    private String coverageTotalTax;

    /**
     * the premium discount of coverage total
     */
    private String coverageTotalPremiumDiscount;

    /**
     * the extra premium of coverage total
     */
    private String coverageTotalExtraPremium;

    /**
     * coverageTotalNoClaimDiscount
     */
    private String coverageTotalNoClaimDiscount;

    /**
     * 加费明细
     */
    private List<LiabilityExtraPremiumOutput> extraPremiumDetails;

    /**
     * discount
     */
    private List<LiabilityDiscountPremiumOutput> discountPremiumDetails;

    /**
     * 分步计算明细
     */
    private Map<String, Object> calResultStepDetail;

}
