/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.payment;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleMockup;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PaymentPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.common.processor.payment.errorcdoes.PaymentHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.processor.payment.result.CreatePaymentResult;
import com.zatech.genesis.sales.journey.integration.payment.request.PaymentOrderRequest;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.payment.PaymentRequestConverter;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@UIModelBusinessHandler(name = "payment", kind = BusinessHandlerKind.finance, desc = "Create payment request")
public class PaymentBusinessHandler implements IAuthBusinessHandler<PaymentPhase, BasicUiModel, PaymentOrderRequest>, IBusinessHandleTrailSupport<PaymentPhase, PaymentOrderRequest, BasicUiModel>, IBusinessHandleMockup<PaymentPhase, BasicUiModel, PaymentOrderRequest> {

    @Autowired
    private PaymentRequestConverter paymentRequestConverter;

    @Override
    public PaymentPhase[] supportedPhases() {
        return new PaymentPhase[] {PaymentPhase.payment};
    }

    @Override
    public Result onContinueError(Exception e, PaymentPhase paymentPhase, BasicUiModel basicUIModel, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        throw CommonException.byErrorAndCause(PaymentHandleErrorCodes.CREATE_PAYMENT_ERROR, e);
    }

    @Override
    public Result onContinueSucceed(Result result, PaymentPhase paymentPhase, BasicUiModel basicUIModel, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        return result;
    }

    @Override
    public Result onStop(PaymentPhase paymentPhase, BasicUiModel basicUIModel, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        return null;
    }

    @Override
    public FlowStrategy handle(PaymentPhase paymentPhase, BasicUiModel basicUIModel, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        paymentRequestConverter.convert(basicUIModel, paymentOrderRequest, businessHandleContext);


        return FlowStrategy.Continue;
    }

    @Override
    public boolean shouldMock(PaymentPhase phase, BasicUiModel businessModel, PaymentOrderRequest param, BusinessHandleContext context) {
        return false;
    }

    @Override
    public Result mock(PaymentPhase phase, BasicUiModel businessModel, PaymentOrderRequest param, BusinessHandleContext context) {
        CreatePaymentResult result = StaticJsonParser.fromJsonString("{\"thirdPartyOrderNo\":\"P_226625931198469\",\"amount\":\"1982.42\",\"orderNo\":\"2024026626\",\"frontendCancelUrl\":\"http://uniqa-web-uniqa-verify.verify.eks.za-gj-aws.net/en/motor/d2c/srb/result/payment/cancel?clientId=Wm1hcnQ6dW5pcWFzcmI6SFE&module=m_551f7a8fe58c4ce586cdba1ca968d103&orderNo=2024026620\",\"frontendErrorUrl\":\"http://uniqa-web-uniqa-verify.verify.eks.za-gj-aws.net/en/motor/d2c/srb/result/payment/failure?clientId=Wm1hcnQ6dW5pcWFzcmI6SFE&module=m_551f7a8fe58c4ce586cdba1ca968d103&orderNo=2024026620\",\"currency\":\"EUR\",\"status\":\"PAYING\",\"payDate\":\"2024-06-04T17:41:07.448+08:00\",\"backendReturnUrl\":\"https://ecg.test.upc.ua/go/enter\",\"formParams\":{\"PurchaseTime\":\"150611110821\",\"Currency\":\"980\",\"Signature\":\"m4fSDspVmzm23zpSsetiaSADFTQXkOe3/z/3LwtYG/++jNI49qUvuIV0iD74H+3onnVrNRNtSM9lQmZWWHZ0fAPAxSny89Z6yMcFrHo/JvuRDJU2R9wTatv5KF5rMjkl3BCEYI4i5eWb3rx9QByYJsUCYikK+YlVBfv3jDIg0Xo=\",\"TerminalID\":\"E7882628\",\"MerchantID\":\"1754628\",\"TotalAmount\":\"1000\",\"OrderID\":175820321,\"locale\":\"ru\"}}", CreatePaymentResult.class);
        result.getFormParams().put("OrderId", RandomStringUtils.randomNumeric(1000000000, 999999999));
        return result;
    }

}
