/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.intent;

import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.leadsform.param.LeadsForm;
import com.zatech.genesis.sales.journey.client.biz.common.converter.ConvertLeadsInfoContext;
import com.zatech.genesis.sales.journey.integration.c360.outer.OuterCustomer360Service;
import com.zatech.genesis.sales.journey.integration.c360.request.LeadsFormRequest;
import com.zatech.genesis.sales.journey.integration.compliance.ComplianceAdapter;
import com.zatech.genesis.sales.journey.order.api.PolicyRecordManager;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.intent.param.IntentEntity;
import com.zatech.genesis.sales.journey.uniqa.leads.convert.IntentObjectConverter;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.octopus.common.util.AssertUtil;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static java.util.Optional.ofNullable;

@Slf4j
@Component
public class TimelySend2C360Handler {

    public static final String COMPLIANCE = "COMPLIANCE";

    @Autowired
    private UIModelServiceFactory uiModelServiceFactory;

    @Autowired
    private IntentObjectConverter converter;

    @Autowired
    private ComplianceAdapter complianceAdapter;

    @Autowired
    private OuterCustomer360Service outerCustomer360Service;

    @Autowired
    private PolicyRecordManager policyRecordManager;

    public void handleLeads(LeadsFormRequest leadsFormRequest, LeadsForm leadsForm, BusinessHandleContext context) {

        LeadsForm.CustomerInfo customer = leadsForm.getCustomer();
        ofNullable(customer.getPhone()).ifPresent(phone -> {
            leadsFormRequest.setMobilePhone(phone.getPhoneNo());
            leadsFormRequest.setCountryCode(phone.getCountryCode());
        });
        leadsFormRequest.setEmail(customer.getEmail());
        leadsFormRequest.setCustomerType(customer.getCustomerType());

        LeadsFormRequest.Detail detail = new LeadsFormRequest.Detail();
        if (PartyTypeEnum.COMPANY.equals(customer.getCustomerType())) {
            detail.setOrganizationName(customer.getOrganizationName());
            detail.setCompanyContactPersonName(customer.getCompanyContactPersonName());
            detail.setCity(customer.getCity());
            detail.setZipCode(customer.getZipCode());
            detail.setCompanyAddress(customer.getCompanyAddress());
            detail.setIdType(ofNullable(customer.getIdType()).map(type -> String.valueOf(OrganizationIDTypeEnum.valueOf(type).getCode())).orElse(null));
        } else {
            detail.setFirstName(customer.getFirstName());
            detail.setLastName(customer.getLastName());
            detail.setMiddleName(customer.getMiddleName());
            detail.setIdType(ofNullable(customer.getIdType()).map(type -> String.valueOf(CertiTypeEnum.valueOf(type).getCode())).orElse(null));
            detail.setCitizenShip(customer.getCitizenship());
        }
        detail.setLanguage(ofNullable(leadsForm.getLanguage()).orElse(TraceOp.getLanguage()));
        detail.setIdNumber(customer.getIdNumber());
        detail.setDateOfBirth(customer.getDateOfBirth());

        //查orderNo
        UIModelOrderOutput<BasicUiModel> orderOutput = uiModelServiceFactory.getQueryService().queryOrThrow(context.getOrder().getOrderId());
        detail.setOrderNo(orderOutput.getOrderNo());


        detail.setGoodsCode(leadsForm.generateGoodsCode());
        detail.setComments(ofNullable(customer.getComments()).orElse(new ArrayList<>()));

        leadsFormRequest.setDetail(detail);
        ofNullable(leadsForm.getMarkingConsent())
            .ifPresent(mc -> leadsFormRequest.setMarkingConsent(mc ? "Y" : "N"));
        ofNullable(leadsForm.getCommunicationConsent())
            .ifPresent(cc -> leadsFormRequest.setCommunicationConsent(cc ? "Y" : "N"));
        ofNullable(leadsForm.getLeads())
            .ifPresent(cc -> leadsFormRequest.setIsLeads(cc ? "Y" : "N"));


        BasicUiModel basicUiModel = orderOutput.getDataOpt().orElse(null);
        AssertUtil.notNull(basicUiModel);

        handleIntent(basicUiModel, leadsFormRequest, false);
    }

    @Async("leadsThreadPoolExecutor")
    @Transactional(rollbackFor = Exception.class)
    public void handleIntent(Long orderId) {
        UIModelOrderOutput<IUIModel> orderOutput = uiModelServiceFactory.getQueryService().queryOrThrow(orderId);
        IUIModel iuiModel = orderOutput.getDataOpt().orElse(null);

        if (!(iuiModel instanceof BasicUiModel basicUiModel)) {
            return;
        }

        if (ObjectUtils.isEmpty(basicUiModel.getIntentForm())) {
            log.info("TimelySend2C360Handler IntentForm is empty");
            return;
        }
        //没有intent项
        if (ObjectUtils.isEmpty(basicUiModel.getIntentForm().getIntentList())) {
            log.info("TimelySend2C360Handler IntentForm.IntentList is empty");
            return;
        }
        LeadsFormRequest leadsFormRequest = converter.convert(basicUiModel, new LeadsFormRequest(), new ConvertLeadsInfoContext(orderOutput));

        if (ObjectUtils.isEmpty(leadsFormRequest) || ObjectUtils.isEmpty(leadsFormRequest.getDetail())) {
            log.info("TimelySend2C360Handler leadsFormRequest or leadsFormRequest.getDetail is empty");
            return;
        }

        handleIntent(basicUiModel, leadsFormRequest, true);
    }

    private void handleIntent(BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest, boolean fromPreviewTrigger) {
        //拿需要重新写回uiModel的数据
        Set<String> requestGoodsCodeSet = new HashSet<>(leadsFormRequest.queryGoodsList());

        Map<String, IntentEntity> goodsCode2IntentEntityMap = ofNullable(basicUiModel.queryIntentList()).orElse(Collections.emptyList())
            .stream()
            .collect(Collectors.groupingBy(IntentEntity::getGoodsCode, Collectors.collectingAndThen(Collectors.toList(), v -> v.get(0))));

        Set<String> intentGoodsCodeSet = goodsCode2IntentEntityMap.keySet();
        //填充leadsFormRequest里面的goodsCode和 comments
        for (String intentGoodsCode : intentGoodsCodeSet) {
            if (!requestGoodsCodeSet.contains(intentGoodsCode)) {
                leadsFormRequest.addGoodsCode(intentGoodsCode);
                Optional.ofNullable(goodsCode2IntentEntityMap.get(intentGoodsCode)).map(IntentEntity::getComment).ifPresent(leadsFormRequest::addComments);
            }
        }
        if (StringUtils.isEmpty(leadsFormRequest.getDetail().getGoodsCode()) || ObjectUtils.isEmpty(leadsFormRequest.getDetail().getComments())) {
            log.info("TimelySend2C360Handler leadsFormRequest.detail goodscode is empty or comments is empty");
            return;
        }

        leadsFormRequest.setNoNeedToCreateForm(fromPreviewTrigger && leadsFormRequest.queryGoodsList().size() == 1);

        log.info("TimelySend2C360Handler leadsFormRequest is {}", JsonParser.toJsonString(leadsFormRequest));
        //发送
        outerCustomer360Service.createLeads(leadsFormRequest);
    }

    //UNIQASEE-15402
    private void buildComments(Long orderId, LeadsFormRequest.Detail detail) {
        val policyRecord = policyRecordManager.query(orderId);
        if (policyRecord != null && StringUtils.isNotBlank(policyRecord.getIssuanceNo())) {
            val complianceCase = complianceAdapter.fetchLastCaseByProposalNo(policyRecord.getIssuanceNo());
            if (complianceCase != null) {
                // 查找是否已有相同 sourceType 和 sourceCode 的 comment
                LeadsFormRequest.Comment existingComment = detail.getComments().stream()
                    .filter(c -> COMPLIANCE.equals(c.getSourceType()) && complianceCase.getCaseNo().equals(c.getSourceCode()))
                    .findFirst()
                    .orElseGet(() -> {
                        // 如果不存在，则创建一个新的 comment 并加入列表
                        LeadsFormRequest.Comment newComment = new LeadsFormRequest.Comment();
                        newComment.setSourceCode(complianceCase.getCaseNo());
                        newComment.setSourceType(COMPLIANCE);
                        newComment.setResultMsgList(new ArrayList<>());
                        detail.getComments().add(newComment);
                        return newComment;
                    });

                // 将组织的 comments 添加到 existingComment 中
                if (CollectionUtils.isNotEmpty(complianceCase.getOrganizations())) {
                    complianceCase.getOrganizations().forEach(x -> {
                        LeadsFormRequest.ResultMsg resultMsg = new LeadsFormRequest.ResultMsg();
                        resultMsg.setMsgValue(x.getComments());
                        if (CollectionUtils.isEmpty(existingComment.getResultMsgList())) {
                            existingComment.setResultMsgList(new ArrayList<>());
                        }
                        existingComment.getResultMsgList().add(resultMsg);
                    });
                }

                // 将客户的 comments 添加到 existingComment 中
                if (CollectionUtils.isNotEmpty(complianceCase.getCustomers())) {
                    complianceCase.getCustomers().forEach(x -> {
                        LeadsFormRequest.ResultMsg resultMsg = new LeadsFormRequest.ResultMsg();
                        resultMsg.setMsgValue(x.getComments());
                        if (CollectionUtils.isEmpty(existingComment.getResultMsgList())) {
                            existingComment.setResultMsgList(new ArrayList<>());
                        }
                        existingComment.getResultMsgList().add(resultMsg);
                    });
                }
            }
        }
    }

}
