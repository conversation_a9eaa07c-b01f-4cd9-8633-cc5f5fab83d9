/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer;

import com.zatech.genesis.partner.api.dto.response.AgentResponse;
import com.zatech.genesis.portal.toolbox.share.function.LazySupplier;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ExtensionContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.UserInfoHolder;
import com.zatech.genesis.sales.journey.integration.notification.NotificationCommand;
import com.zatech.genesis.sales.journey.share.dto.notification.send.AttachmentDTO;
import com.zatech.genesis.sales.journey.share.dto.notification.send.BusinessNoTuple;
import com.zatech.genesis.sales.journey.share.dto.notification.send.RecipientDTO;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: weizhen.kong
 */
public class QuickOfferNotificationCommand implements NotificationCommand {

    private final List<AttachmentDTO> attachment;

    private final LazySupplier<AgentResponse> managerResponseSupplier;

    private final LazySupplier<AgentResponse> agentResponseSupplier;

    private final Map<String, Object> simpleFactors;

    @Getter
    @Setter
    private BusinessNoTuple triggerBusinessNo;

    public QuickOfferNotificationCommand(List<AttachmentDTO> attachment, ContextHolder contextHolder, Map<String, Object> simpleFactors) {
        this.attachment = attachment;
        OrderContextHolder orderContextHolder = contextHolder.getTransactionOrderContext().orElse(null);
        this.managerResponseSupplier = new LazySupplier<>(() -> Optional.of(contextHolder).flatMap(ContextHolder::getUserInfoHolder).flatMap(UserInfoHolder::getAgentDetail).orElse(null));
        this.agentResponseSupplier = new LazySupplier<>(() -> Optional.ofNullable(orderContextHolder)
            .flatMap(OrderContextHolder::getExtensionContextHolder).flatMap(ExtensionContextHolder::getAgentInfo).orElse(null));
        this.simpleFactors = simpleFactors;
    }

    @Override
    public RecipientDTO getRecipientDTO() {
        var recipientDTO = new RecipientDTO();
        String agentEmail = Optional.ofNullable(agentResponseSupplier.get()).map(AgentResponse::getEmail).filter(StringUtils::isNotBlank).orElse("");
        String userEmail = Optional.ofNullable(managerResponseSupplier.get()).map(AgentResponse::getEmail).filter(StringUtils::isNotBlank).orElse("");

        recipientDTO.setDirectRecipientDestinations(List.of(agentEmail));
        if (!StringUtils.equals(agentEmail, userEmail) && StringUtils.isNotBlank(userEmail)) {
            recipientDTO.setDirectCcRecipientDestinations(List.of(userEmail));
        }

        return recipientDTO;
    }

    @Override
    public List<AttachmentDTO> getAttachmentList() {
        return attachment;
    }

    @Override
    public Map<String, Object> getSimpleFactors() {
        return simpleFactors;
    }

    @Override
    public String getTriggerPoint() {
        return "QUOTATION_COMPLETE";
    }

}