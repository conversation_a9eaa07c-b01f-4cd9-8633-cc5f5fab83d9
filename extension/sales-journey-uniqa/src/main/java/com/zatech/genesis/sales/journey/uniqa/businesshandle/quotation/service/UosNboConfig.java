package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "sj.uosnbo")
public class UosNboConfig {

    private boolean uosCheckEnabled;

    private boolean nboCheckEnabled;
}
