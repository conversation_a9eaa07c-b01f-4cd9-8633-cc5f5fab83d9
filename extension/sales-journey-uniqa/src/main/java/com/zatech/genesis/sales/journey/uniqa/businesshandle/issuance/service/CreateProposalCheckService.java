/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.IDataContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.dsl.DataValidateMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.datavalidate.dsl.ValidateDataValidateDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.ExecuteBaseResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.IExecutorRunner;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.fiequery.handler.IDocTypeRemainHandler;
import com.zatech.genesis.sales.journey.uniqa.datavalidate.filecheck.param.FileCheckParam;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.List;
import java.util.Objects;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/22 19:04
 **/
@Component
public class CreateProposalCheckService {

    @Autowired
    IExecutorRunner executorRunner;

    private final List<IDocTypeRemainHandler> docTypeRemainHandlers;

    @Autowired
    public CreateProposalCheckService(List<IDocTypeRemainHandler> docTypeRemainHandlers) {
        this.docTypeRemainHandlers = docTypeRemainHandlers;
    }

    public boolean checkIfIssue(BasicUiModel uiModel, IDataContext context) {
        //renew时间上是否存在gap(currentEffectiveDate.compareTo(oldPolicyEndDate) > 0)
        if (uiModel.getScenario() == ScenarioTypeEnum.renew) {
            if (notNeedCheck(uiModel)) {
                return true;
            }
            //renew场景不存在gap，可以直接创建投保单。 或者有gap但是没有必传，也可以直接创建投保
            return !isRenewInGap(uiModel, context) || hasNoNecessaryDocTypes(uiModel);
        }
        //没有必传文件，可以创建投保单
        return hasNoNecessaryDocTypes(uiModel);
    }

    private boolean notNeedCheck(BasicUiModel basicUiModel) {
        return basicUiModel.getAllGoods().stream()
                .filter(Objects::nonNull)
                .filter(x -> Objects.equals(true, x.getSelected()))
                .filter(x -> Objects.nonNull(x.getElements()))
                .anyMatch(x -> Objects.equals("decline", x.getElements().getComplianceTask()));

    }

    private boolean isRenewInGap(BasicUiModel uiModel, IDataContext context) {
        String orderNo = context.getOrder().getOrderNo();
        if (context.getOrder().isSubOrder() && context.getOrder().getUiModelOpt().isEmpty()) {
            orderNo = context.getOrder().getTransactionOrderContextOpt().map(OrderContext::getOrderNo).orElse(orderNo);
        }
        var request = createValidateDslTemplate(uiModel);
        var results = executorRunner.runDataValidators(orderNo, request);
        return results.headOption()
            .map(ExecuteBaseResult::isSuccess)
            .orElse(false);

    }

    private ValidateDataValidateDslTemplate createValidateDslTemplate(BasicUiModel uiModel) {
        ValidateDataValidateDslTemplate request = new ValidateDataValidateDslTemplate();

        DataValidateMetaInfo.Provider provider = new DataValidateMetaInfo.Provider();
        provider.setName("fileCheck");

        FileCheckParam param = new FileCheckParam();
        param.setCommercialInsuranceGoods(uiModel.getCommercialInsuranceGoods());
        param.setTrafficCompulsoryInsuranceGoods(uiModel.getTrafficCompulsoryInsuranceGoods());

        request.getMetadata().setProviders(List.of(provider));
        request.getSpec().setParams(StaticJsonParser.fromObjectToMap(param));

        return request;
    }


    private boolean hasNoNecessaryDocTypes(BasicUiModel uiModel) {
        List<String> docTypes = docTypeRemainHandlers.stream()
            .filter(handler -> handler.shouldRemain(uiModel))
            .map(IDocTypeRemainHandler::docType)
            .toList();
        //非必传的条件：docTypes为俩（others 和 vehicle purchase invoice）且 非新车（新车必须得上传图片）
        return CollectionUtils.isNotEmpty(docTypes) && docTypes.size() == 2 && !Boolean.TRUE.equals(uiModel.getVehicleInfo().getNewVehicle());
    }

}
