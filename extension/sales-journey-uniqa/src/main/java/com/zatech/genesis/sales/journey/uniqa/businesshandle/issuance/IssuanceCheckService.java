package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.order.api.errorcode.PolicyRecordErrorCode;
import com.zatech.genesis.sales.journey.uniqa.errorcode.IssuanceCheckErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;

import java.util.Map;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class IssuanceCheckService {

    @Autowired
    private IOuterPolicyService iOuterPolicyService;

    private static final Map<GoodsCodeEnum, IErrorCode> WITHDRAWN_ERROR_CODE_MAP = Map.of(
        GoodsCodeEnum.MHULL, IssuanceErrorCodes.ISSUANCE_STATUS_IS_WITHDRAWN_MHULL,
        GoodsCodeEnum.MTPL, IssuanceErrorCodes.ISSUANCE_STATUS_IS_WITHDRAWN_MTPL,
        GoodsCodeEnum.MTPL_Riders, IssuanceErrorCodes.ISSUANCE_STATUS_IS_WITHDRAWN_MTPL_RIDER,
        GoodsCodeEnum.PA, IssuanceErrorCodes.ISSUANCE_STATUS_IS_WITHDRAWN_PA
    );

    public void checkStatus(String issuanceNo) {
        IssuanceResponse issuance = iOuterPolicyService.getIssuance(issuanceNo, YesNoEnum.YES);
        if (issuance == null) {
            return;
        }
        if (issuance.getIssuanceStatus() == IssuanceStatusEnum.WITHDRAWN) {
            throw CommonException.byError(WITHDRAWN_ERROR_CODE_MAP.getOrDefault(GoodsCodeEnum.queryByGoodsCode(issuance.getGoodsCode()), PolicyRecordErrorCode.ISSUANCE_STATUS_IS_ILLEGAL));
        }
    }

    /**
     * 校验当前的单子是否已经被b端人员分配锁单
     * @param contextHolder
     */
    public void checkIfLockByUser(ContextHolder contextHolder) {
        contextHolder.getOrderContextHolders().stream()
            .map(OrderContextHolder::getWaitingForIssuance)
            .filter(Optional::isPresent)
            .map(Optional::get)
            //task不为空，且对应的externalFlag 不等于Yes时锁单。
            .filter(issuanceResponse -> issuanceResponse.getTask() != null && issuanceResponse.getTask().getExternalFlag() != YesNoEnum.YES)
            .findAny()
            .ifPresent(data -> {
                throw CommonException.byError(IssuanceCheckErrorCode.CURRENT_PROPOSAL_HAS_LOCKED);
            });
    }
}
