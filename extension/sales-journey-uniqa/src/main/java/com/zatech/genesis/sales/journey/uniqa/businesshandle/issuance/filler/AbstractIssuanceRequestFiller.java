/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.filler;

import com.zatech.genesis.policy.api.reqeust.IssuanceRelationDetailRequest;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:22
 **/
public abstract class AbstractIssuanceRequestFiller implements IIssuanceRequestFiller{

    @Override
    public void fillRequest(List<IssuanceRequest> requestList, BasicUiModel uiModel, ContextHolder contextHolder, BusinessHandleContext context) {
        setRelation(requestList, uiModel, contextHolder, context);
    }

    protected void setRelation(List<IssuanceRequest> requestList, BasicUiModel uiModel, ContextHolder contextHolder, BusinessHandleContext context) {
        String relationPolicyTemplateCode = uiModel.getRelationPolicyTemplateCode();
        String relationPolicyNo = contextHolder.getTransactionOrderNo().orElseThrow();
        ScenarioTypeEnum scenario = uiModel.getScenario();

        if (shouldSetRelationForSalesJourney(scenario, requestList)) {
            setRelationDetails(requestList, relationPolicyTemplateCode, relationPolicyNo, requestList.size());
            return;
        }

        //到了这一步判断是不是2A的场景，否则就是D2C单个出单。
        if (scenario.isD2A()) {
            if (shouldSetRelationForTransactionOrder(contextHolder, context)) {
                setRelationDetails(requestList, relationPolicyTemplateCode, relationPolicyNo, contextHolder.getOrderContextHolders().size());
                return;
            }

            if (shouldSetRelationForSubOrders(contextHolder)) {
                List<OrderContext> subOrderContexts = contextHolder.getTransactionOrderContext().orElseThrow().getOrderContext().getSubOrderContexts();
                setRelationDetails(requestList, relationPolicyTemplateCode, relationPolicyNo, subOrderContexts.size());
            }
        }
    }

    /**
     * D2C，只需要判断过筛goods的request数量即可
     *
     * @param scenario
     * @param requestList
     * @return
     */
    private boolean shouldSetRelationForSalesJourney(ScenarioTypeEnum scenario, List<IssuanceRequest> requestList) {
        return scenario.isD2C() && requestList.size() > 1;
    }

    /**
     * 2A场景还需要判断在主order的情况下，是不是只有一个单子，一个就不需要设置了，两个无脑塞，因为不管人核与否都得要
     *
     * @param contextHolder
     * @param context
     * @return
     */
    private boolean shouldSetRelationForTransactionOrder(ContextHolder contextHolder, BusinessHandleContext context) {
        return context.getOrder().isTransactionOrder() && contextHolder.getOrderContextHolders().size() > 1;
    }

    /**
     * 子订单进来，说明是在AgentPortal页面操作
     * 先判断主订单的UIModel是不是from D2A的，因为如果是2c转2A的不需要设置，2c转2A只会变更子订单的UIModel的scenario
     * 如果主订单是2A的，判断是对应子订单的数量是不是大于1，是的话塞上relationPolicyNo
     *
     * @param contextHolder
     * @return
     */
    private boolean shouldSetRelationForSubOrders(ContextHolder contextHolder) {
        return contextHolder.getTransactionOrderContext()
            .map(orderContextHolder -> {
                BasicUiModel transactionModel = orderContextHolder.getOrderContext().getUiModel().getDataOrNull();
                boolean isTransactionFromD2A = transactionModel.getScenario().isD2A();
                List<OrderContext> subOrderContexts = orderContextHolder.getOrderContext().getSubOrderContexts();
                return isTransactionFromD2A && subOrderContexts.size() > 1;
            }).orElse(false);
    }

    private void setRelationDetails(List<IssuanceRequest> requestList, String relationPolicyTemplateCode, String relationPolicyNo, int numberOfIssuance) {
        IssuanceRelationDetailRequest request = new IssuanceRelationDetailRequest();
        request.setRelationPolicyTemplateCode(relationPolicyTemplateCode);
        request.setRelationPolicyNo(relationPolicyNo);
        request.setNumberOfIssuance(numberOfIssuance);

        List<IssuanceRelationDetailRequest> issuanceRelationDetailList = List.of(request);
        requestList.forEach(req -> req.setIssuanceRelationDetailList(issuanceRelationDetailList));
    }

}
