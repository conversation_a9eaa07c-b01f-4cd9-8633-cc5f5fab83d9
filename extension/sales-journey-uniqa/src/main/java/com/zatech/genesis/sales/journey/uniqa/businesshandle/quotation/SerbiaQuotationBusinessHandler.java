/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.trail.TrailContext;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.param.QuotationParam;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.plugin.api.enums.TrailStepEnum;
import com.zatech.genesis.sales.journey.uniqa.uimodel.serbia.SerbiaUiModel;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024.04.25
 */
@Slf4j
@UIModelBusinessHandler(name = "quotation", kind = BusinessHandlerKind.market, desc = "Serbia auto quotation business handler", tags = Consts.SERBIA)
public class SerbiaQuotationBusinessHandler extends BaseQuotationBusinessHandler implements IAuthBusinessHandler<MarketPhase, SerbiaUiModel, QuotationParam> {

    @Override
    public MarketPhase[] supportedPhases() {
        return new MarketPhase[] {MarketPhase.quotation, MarketPhase.quickQuotation, MarketPhase.previewQuotation};
    }

    @Override
    public Result onContinueError(Exception e, MarketPhase marketPhase, SerbiaUiModel serbiaUIModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.onContinueError(e, marketPhase, serbiaUIModel, quotationParam, businessHandleContext);
    }

    @Override
    public Result onContinueSucceed(Result result, MarketPhase marketPhase, SerbiaUiModel serbiaUIModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.onContinueSucceed(result, marketPhase, serbiaUIModel, quotationParam, businessHandleContext);
    }

    @Override
    public Result onStop(MarketPhase marketPhase, SerbiaUiModel serbiaUIModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.mockResult();
    }

    @Override
    public FlowStrategy handle(MarketPhase marketPhase, SerbiaUiModel serbiaUIModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        if ((serbiaUIModel.getScenario() == ScenarioTypeEnum.salesJourney || serbiaUIModel.getScenario() == ScenarioTypeEnum.salesJourneyAgent || serbiaUIModel.getScenario() == ScenarioTypeEnum.renew)
            && businessHandleContext.getTrailOpt().map(TrailContext::getStep).orElse(TrailStepEnum.QUICK_QUOTE.name()).equals(TrailStepEnum.QUICK_QUOTE.name())) {
            log.info("srb page init not quotation");
            serbiaUIModel.setTrafficCompulsoryInsuranceGoods(null);
        }
        super.handle(marketPhase, serbiaUIModel, quotationParam, businessHandleContext);
        return FlowStrategy.Continue;
    }

}
