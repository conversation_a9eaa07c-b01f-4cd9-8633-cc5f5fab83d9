/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result;

import com.zatech.gaia.resource.graphene.market.campaign.CampaignCategoryEnum;
import com.zatech.gaia.resource.graphene.market.campaign.CampaignPremiumDiscountTypeEnum;

import java.util.List;

import lombok.Data;

@Data
public class CampaignRelatingOutput {

    private String campaignCode;

    private CampaignCategoryEnum campaignCategory;

    private CampaignPremiumDiscountTypeEnum discountType;

    private String periodTotalDiscount;

    private String coverageTotalDiscount;

    private List<Payer> payers;

    @Data
    public static class Payer {

        private Integer payerType;

        private String payerCode;

        private String payerSubCode;

        private String discount;

        private String discountRate;

        private Integer paymentMethod;

    }

}
