/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.file.manager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.zatech.genesis.policy.api.base.IssuanceAttachmentBase;
import com.zatech.genesis.policy.api.base.PolicyAttachmentBase;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderQueryService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelOrderUpdateService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.UpdateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Attachment;
import com.zatech.genesis.sales.journey.infra.entity.legacy.PsOrderFrontendContext;
import com.zatech.genesis.sales.journey.infra.repository.legacy.PsOrderFrontendContextRepository;
import com.zatech.genesis.sales.journey.infra.repository.legacy.PsPolicyRecordRepository;
import com.zatech.genesis.sales.journey.integration.file.FileAdapter;
import com.zatech.genesis.sales.journey.integration.file.response.CopyFileResponse;
import com.zatech.genesis.sales.journey.order.api.PolicyRecordManager;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.file.model.FrontContextAttachment;
import com.zatech.genesis.sales.journey.uniqa.converter.AttachmentConverter;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.fiequery.FileQueryConstants;
import com.zatech.genesis.sales.journey.uniqa.errorcode.FileProcessorErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.CopyInfo;
import com.zatech.octopus.common.util.AssertUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.zatech.genesis.sales.journey.api.file.enums.FileAdditionalSubCategoryEnum.ISSUANCE_DOCUMENT;

/**
 * <AUTHOR>
 * @date 2024/8/27 15:57
 **/
@Slf4j
@Component
public class FileManager {

    @Autowired
    private PsPolicyRecordRepository psPolicyRecordRepository;

    @Autowired
    private PsOrderFrontendContextRepository psOrderFrontendContextRepository;

    @Autowired
    private UIModelOrderUpdateService uiModelOrderUpdateService;

    @Autowired
    private UIModelOrderQueryService uiModelOrderQueryService;

    @Autowired
    PolicyRecordManager policyRecordManager;

    @Autowired
    private FileAdapter fileAdapter;

    private static final String POLICY_FILE_ADDITIONAL_BIZ_NO = "PolicyNumber";

    private static final String ISSUANCE_FILE_ADDITIONAL_BIZ_NO = "IssuanceNumber";

    /**
     * 查询投保单文件
     *
     * @param issuanceAttachmentBases
     * @return
     */
    public List<Attachment> queryProposalFile(List<IssuanceAttachmentBase> issuanceAttachmentBases, IssuanceResponse issuanceResponse) {
        if (CollectionUtils.isEmpty(issuanceAttachmentBases)) {
            return Collections.emptyList();
        }

        List<Attachment> attachments = issuanceAttachmentBases.stream().map(AttachmentConverter.INSTANCE::issuanceAttachmentBaseToAttachment).toList();

        if (isOldData(issuanceResponse::getBizApplyNo)) {
            return extractAttachments(issuanceResponse.getIssuanceNo(), issuanceResponse.getGoodsId());
        }
        return attachments;
    }

    /**
     * 查询保单文件
     *
     * @param policyAttachmentBases
     * @return
     */
    public List<Attachment> queryPolicyFile(List<PolicyAttachmentBase> policyAttachmentBases, PolicyResponse policyResponse) {
        if (CollectionUtils.isEmpty(policyAttachmentBases)) {
            return Collections.emptyList();
        }
        List<Attachment> attachments = policyAttachmentBases.stream().map(AttachmentConverter.INSTANCE::policyAttachmentBaseToAttachment).toList();

        if (isOldData(policyResponse::getBizApplyNo)) {
            return extractAttachments(policyResponse.getIssuanceNo(), policyResponse.getGoodsId());
        }
        return attachments;
    }

    public List<Attachment> extractAttachments(String issuanceNo, Long goodsId) {
        Long psOrderId = psPolicyRecordRepository.queryMainOrderIdByIssuanceNo(issuanceNo);
        if (psOrderId != null) {
            Optional<PsOrderFrontendContext> frontendContextOpt = psOrderFrontendContextRepository.findByOrderId(psOrderId);
            if (frontendContextOpt.isPresent()) {
                JsonMap map = StaticJsonParser.fromJsonStringToMap(frontendContextOpt.get().getContext());
                return Optional.ofNullable(map.get("fileMap"))
                    .map(d -> extractFromData(d, goodsId))
                    .orElseGet(() -> extractFromData(map.get("MHULL"), null));
            }
        }
        return Collections.emptyList();
    }

    private List<Attachment> extractFromData(Object data, Long goodsId) {
        List<List<FrontContextAttachment>> lists;
        if (goodsId != null) {
            Object o = StaticJsonParser.fromObjectToMap(data).get(String.valueOf(goodsId));
            lists = StaticJsonParser.fromJsonString(StaticJsonParser.toJsonString(o), new TypeReference<>() {

            });
        } else {
            lists = StaticJsonParser.fromJsonString(StaticJsonParser.toJsonString(data), new TypeReference<>() {

            });
        }
        return convertAttachment(lists);
    }


    public boolean isOldData(Supplier<String> bizApplyNoSupplier) {
        return Optional.ofNullable(bizApplyNoSupplier.get()).map(d -> d.startsWith("z_")).orElse(false);
    }

    public void resetCopyAttachment(List<PolicyRecordInfoOutput> policyRecords) {
        policyRecords.forEach(policyRecord -> {
            UIModelOrderOutput<IUIModel> query = uiModelOrderQueryService.query(policyRecord.getOrderId());
            BasicUiModel uiModel = query.getUiModel().getDataOrNull();
            uiModel.setCopyInfo(null);
            uiModelOrderUpdateService.updateOrThrow(query.getOrderId(), null, new UpdateUIModelInput<>(uiModel, null, false));
        });
    }

    /**
     * 将frontContext数据转为标准attachment
     *
     * @param attachmentList
     */
    public List<Attachment> convertAttachment(List<List<FrontContextAttachment>> attachmentList) {
        if (attachmentList == null || CollectionUtils.isEmpty(attachmentList)) {
            return Collections.emptyList();
        }
        return attachmentList.stream()
            .filter(Objects::nonNull)
            .flatMap(List::stream)
            .map(frontContextAttachment -> {
                var attachment = new Attachment();
                attachment.setAttachmentName(frontContextAttachment.getAttachmentName());
                attachment.setAttachmentUrl(frontContextAttachment.getAttachmentUrl());
                attachment.setDocumentType(frontContextAttachment.getAttachmentType());
                return attachment;
            })
            .toList();
    }

    /**
     * Upload页面回显查询文件
     *
     * @param attachments
     * @param policyRecords
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<Attachment> queryFile(List<Attachment> attachments, List<PolicyRecordInfoOutput> policyRecords, String orderNo, ContextHolder contextHolder) {
        //存在需要copy的文件
        UIModelOrderOutput<IUIModel> query = uiModelOrderQueryService.query(orderNo);
        BasicUiModel uiModel = query.getUiModel().getDataOrNull();
        if (uiModel.getCopyInfo() != null) {
            log.info("Batch copy attachment");
            return batchCopy(uiModel.getCopyInfo(), uiModel, orderNo, policyRecords);
        }
        //投保单上没有文件，return 即可
        if (CollectionUtils.isEmpty(attachments)) {
            return attachments;
        }
        //有文件需要判断是否是老单子，是老单子就需要查 frontContext
        Optional<IssuanceResponse> issuanceResponseOpt = contextHolder.getOrderContextHolders().stream().findFirst().flatMap(OrderContextHolder::getWaitingForIssuance);
        if (isOldData(() -> issuanceResponseOpt.isEmpty() ? null : issuanceResponseOpt.get().getBizApplyNo())) {
            log.info("Extract attachments from frontContext");
            //到了这一步就不会是主订单，子订单policyRecords默认只有一个
            return extractAttachments(policyRecords.get(0).getIssuanceNo(), policyRecords.get(0).getGoodsId());
        }
        return attachments;
    }

    private List<Attachment> batchCopy(CopyInfo copyInfo, BasicUiModel uiModel, String orderNo, List<PolicyRecordInfoOutput> policyRecords) {
        if (Boolean.TRUE.equals(copyInfo.getHasBeenCopy())) {
            return copyInfo.getCopyAttachments();
        }

        List<String> fileUniqueCodes = copyInfo.getCopyAttachments().stream().map(Attachment::getAttachmentUrl).toList();
        List<CopyFileResponse> cloneFileResponses = fileAdapter.batchCopy(fileUniqueCodes, getAdditionalInfo(orderNo));
        log.info("Batch copy response : {}", StaticJsonParser.toJsonString(cloneFileResponses));

        List<Attachment> copyAttachment = new ArrayList<>();

        Map<String, String> oldNewFileCodeMap = cloneFileResponses.stream()
            .collect(Collectors.toMap(CopyFileResponse::getFileUniqueCode, CopyFileResponse::getNewFileUniqueCode, (x, y) -> x));

        copyInfo.getCopyAttachments().forEach(attachment -> {
            if (oldNewFileCodeMap.get(attachment.getAttachmentUrl()) != null) {
                attachment.setAttachmentUrl(oldNewFileCodeMap.get(attachment.getAttachmentUrl()));
                copyAttachment.add(attachment);
            }
        });
        copyInfo.setHasBeenCopy(true);
        copyInfo.setCopyAttachments(copyAttachment);
        uiModel.setCopyInfo(copyInfo);
        policyRecords.forEach(policyRecord -> {
            UIModelOrderOutput<IUIModel> query = uiModelOrderQueryService.query(policyRecord.getOrderId());
            if (Objects.nonNull(query.getUiModel())) {
                BasicUiModel model = query.getUiModel().getDataOrNull();
                model.setCopyInfo(copyInfo);
                uiModelOrderUpdateService.updateOrThrow(query.getOrderId(), null, new UpdateUIModelInput<>(uiModel, null, false));
            }
        });
        uiModelOrderUpdateService.updateOrThrow(orderNo, null, new UpdateUIModelInput<>(uiModel, null, false));
        return copyAttachment;
    }

    /**
     * 新的兼容老的，保存frontContext
     *
     * @param records
     * @param attachments
     * @param context
     */
    public void resetOrSaveFrontContext(List<PolicyRecordInfoOutput> records, List<Attachment> attachments, BusinessHandleContext context) {
        //文件不为空，reset copy的文件
        BasicUiModel basicUiModel = context.getOrder().getUiModel().getDataOrNull();
        AssertUtil.notNull(basicUiModel);
        if (basicUiModel.getCopyInfo() != null) {
            resetCopyAttachment(records);
            return;
        }

        //拿到主orderId处理frontContext。兼容老的
        Long mainOrderId;
        if (context.getOrder().isSubOrder()) {
            mainOrderId = context.getOrder().getTransactionId();
        } else {
            mainOrderId = context.getOrder().getOrderId();
        }

        Optional<IssuanceResponse> issuanceResponseOpt = new ContextHolder(context).getOrderContextHolders().stream().findFirst().flatMap(OrderContextHolder::getWaitingForIssuance);

        if (!isOldData(() -> issuanceResponseOpt.isEmpty() ? null : issuanceResponseOpt.get().getBizApplyNo())) {
            return;
        }
        log.info("Current is processing old data");
        Optional<PsOrderFrontendContext> frontendContextOpt = psOrderFrontendContextRepository.findByOrderId(mainOrderId);
        if (frontendContextOpt.isEmpty()) {
            log.warn("frontendContextOpt is empty");
            return;
        }
        PsOrderFrontendContext psOrderFrontendContext = frontendContextOpt.get();
        JsonMap map = StaticJsonParser.fromJsonStringToMap(psOrderFrontendContext.getContext());

        List<List<FrontContextAttachment>> lists = convertFrontContextAttachmentLists(basicUiModel, attachments);

        map.put("MHULL", lists);

        JsonMap fileMap = Optional.ofNullable(map.get("fileMap"))
            .map(StaticJsonParser::fromObjectToMap)
            .orElseGet(JsonMap::new);

        //更新文件
        records.forEach(record -> {
            fileMap.put(String.valueOf(record.getGoodsId()), lists);
            map.put("fileMap", fileMap);
        });

        psOrderFrontendContext.setContext(StaticJsonParser.toJsonString(map));

        psOrderFrontendContextRepository.save(psOrderFrontendContext);
    }

    /**
     * save的时候需要保证结构符合此前前端存放frontcontext的文件存储结构，不存在数据也需要用null占位
     * <p>
     * export const AttachmentTypeMapping = {
     * // 9张
     * 'Vehicle Photo': [0, 1, 2, 3, 4, 5, 6, 7, 19],
     * // 8张
     * 'Photo of Tire': [8, 9, 10, 11, 12, 13, 14, 15],
     * 'Photo of Glass': [16, 17, 18],
     * 'Vehicle Registration Document': [20],
     * 'Vehicle Purchase Invoice': [21],
     * 'Loan Contract': [22],
     * 'Car User Disability Proof': [23],
     * 'Evidence of Special Agreement Discount': [24],
     * 'Others': [25],
     * };
     * <p>
     * export const TOC = {
     * // 9张
     * 'Vehicle Photo': [0, 1, 2, 3, 4, 5, 6, 7, 19],
     * // 8张
     * 'Photo of Tire': [8, 9, 10, 11, 12, 13, 14, 15],
     * 'Photo of Glass': [16, 17, 18],
     * 'Loan Contract': [20],
     * 'Others': [21],
     * };
     *
     * @param basicUiModel
     * @param attachments
     * @return
     */
    public List<List<FrontContextAttachment>> convertFrontContextAttachmentLists(BasicUiModel basicUiModel, List<Attachment> attachments) {
        Map<String, List<Attachment>> groupedData = attachments.stream()
            .collect(Collectors.groupingBy(Attachment::getDocumentType));

        int size = basicUiModel.getScenario().isD2A() ? 26 : 22;
        List<List<FrontContextAttachment>> lists = Stream.generate(ArrayList<FrontContextAttachment>::new)
            .limit(size)
            .collect(Collectors.toList());

        processAttachments(groupedData.get(FileQueryConstants.VEHICLE_PHOTO), lists, 0, 8, 19);
        processAttachments(groupedData.get(FileQueryConstants.PHOTO_OF_TIRE), lists, 8, 16, -1);
        processAttachments(groupedData.get(FileQueryConstants.PHOTO_OF_GLASS), lists, 16, 19, -1);
        if (basicUiModel.getScenario().isD2A()) {
            processAttachments(groupedData.get(FileQueryConstants.VEHICLE_REGISTRATION_DOCUMENT), lists, 20, 21, -1);
            processAttachments(groupedData.get(FileQueryConstants.VEHICLE_PURCHASE_INVOICE), lists, 21, 22, -1);
            processAttachments(groupedData.get(FileQueryConstants.LOAN_CONTRACT), lists, 22, 23, -1);
            processAttachments(groupedData.get(FileQueryConstants.CAR_USER_DISABILITY_PROOF), lists, 23, 24, -1);
            processAttachments(groupedData.get(FileQueryConstants.EVIDENCE_OF_SPECIAL_AGREEMENT_DISCOUNT), lists, 24, 25, -1);
            processAttachments(groupedData.get(FileQueryConstants.OTHERS), lists, 25, 26, 25);
        } else {
            processAttachments(groupedData.get(FileQueryConstants.LOAN_CONTRACT), lists, 20, 21, -1);
            processAttachments(groupedData.get(FileQueryConstants.OTHERS), lists, 21, 22, 21);
        }

        for (int i = 0; i < lists.size(); i++) {
            if (lists.get(i) != null && CollectionUtils.isEmpty(lists.get(i))) {
                // 最后将空列表替换为 null
                lists.set(i, null);
            }
        }
        return lists;
    }

    /**
     * 【startIndex, endIndex)
     *
     * @param attachments   投保单上图片
     * @param lists         转换后储存的图片
     * @param startIndex    开始节点
     * @param endIndex      结束节点
     * @param overflowIndex 溢出的数据所放的节点
     */
    private void processAttachments(List<Attachment> attachments, List<List<FrontContextAttachment>> lists, int startIndex, int endIndex, int overflowIndex) {
        if (CollectionUtils.isEmpty(attachments)) {
            for (int i = startIndex; i < endIndex; i++) {
                lists.set(i, null);
                if (overflowIndex > 0) {
                    lists.set(overflowIndex, null);
                }
            }
        } else {
            for (int i = 0; i < attachments.size(); i++) {
                FrontContextAttachment frontContextAttachment = convertFrontContextAttachment(attachments.get(i));
                if (i < (endIndex - startIndex)) {
                    lists.set(startIndex + i, new ArrayList<>(List.of(frontContextAttachment)));
                } else if (overflowIndex > 0) {
                    lists.set(overflowIndex, Optional.ofNullable(lists.get(overflowIndex))
                        .map(existing -> {
                            existing.add(frontContextAttachment);
                            return existing;
                        }).orElseGet(() -> new ArrayList<>(List.of(frontContextAttachment))));
                }
            }
        }
    }

    public FrontContextAttachment convertFrontContextAttachment(Attachment attachment) {
        FrontContextAttachment frontContextAttachment = new FrontContextAttachment();
        frontContextAttachment.setAttachmentName(attachment.getAttachmentName());
        frontContextAttachment.setAttachmentUrl(attachment.getAttachmentUrl());
        frontContextAttachment.setAttachmentType(attachment.getDocumentType());
        return frontContextAttachment;
    }

    private String getAdditionalInfo(String orderNo) {
        return Optional.ofNullable(query(orderNo))
            .map(pair -> {
                Map<String, String> bizParams =  Map.of(POLICY_FILE_ADDITIONAL_BIZ_NO, pair.getValue(),ISSUANCE_FILE_ADDITIONAL_BIZ_NO, pair.getKey());
                return toAdditionalInfoString(bizParams);
            })
            .orElse(null);
    }

    private String toAdditionalInfoString(Map<String, String> bizParams) {
        if (MapUtils.isEmpty(bizParams)) {
            return null;
        }
        Map<String, String> paramsMaps = new HashMap<>(bizParams);

        paramsMaps.put("domain", ISSUANCE_DOCUMENT.getDomain().getCode());
        paramsMaps.put("subCategory", ISSUANCE_DOCUMENT.getCode());
        return StaticJsonParser.toJsonString(paramsMaps);
    }

    private Pair<String, String> query(String orderNo) {
        UIModelOrderOutput<IUIModel> orderOutput = uiModelOrderQueryService.query(orderNo);
        if (orderOutput.isSubOrder()) {
            //是子订单
            PolicyRecordInfoOutput record = policyRecordManager.query(orderOutput.getOrderId());
            return Optional.ofNullable(record).map(d -> Pair.of(d.getIssuanceNo(), d.getPolicyNo())).orElse(null);
        }

        List<OrderContext> subOrderContexts = Optional.ofNullable(orderOutput.getSubOrderContexts())
            .filter(CollectionUtils::isNotEmpty)
            .orElseThrow(() -> CommonException.byError(FileProcessorErrorCode.SUB_ORDER_NOT_FOUND));

        List<PolicyRecordInfoOutput> policyRecordInfoOutputs = subOrderContexts.stream().map(OrderContext::getOrderId).map(policyRecordManager::query).toList();
        String issuanceNo = policyRecordInfoOutputs.stream().map(PolicyRecordInfoOutput::getIssuanceNo).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
        String policyNo = policyRecordInfoOutputs.stream().map(PolicyRecordInfoOutput::getPolicyNo).filter(StringUtils::isNotBlank).collect(Collectors.joining(","));

        return Pair.of(issuanceNo, policyNo);
    }

}
