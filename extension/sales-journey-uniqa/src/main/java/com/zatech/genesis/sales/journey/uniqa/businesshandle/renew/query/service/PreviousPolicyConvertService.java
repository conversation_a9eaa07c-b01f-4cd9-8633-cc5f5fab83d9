/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service;

import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.context.ConvertPolicyResponseContext;

import java.util.List;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class PreviousPolicyConvertService {

    private final List<AbstractPolicyConvert> previousPolicyConvert;

    public BasicUiModel convert(BasicUiModel model, ConvertPolicyResponseContext context, PolicyResponse policyResponse,
                                IssuanceRequest issuanceRequest) {
        return previousPolicyConvert.stream()
                .filter(policyConvert -> policyConvert.support(model.getScenario()))
                .findFirst()
                .map(policyConvert -> policyConvert.convert(issuanceRequest, model, context, policyResponse))
                .orElse(model);
    }


}