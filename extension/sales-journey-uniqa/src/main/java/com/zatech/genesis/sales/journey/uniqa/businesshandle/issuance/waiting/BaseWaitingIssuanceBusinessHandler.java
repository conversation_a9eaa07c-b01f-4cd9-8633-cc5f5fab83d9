/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.waiting;

import com.google.common.collect.Lists;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.IssuanceBusinessResult;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service.WaitingInsuranceService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.za.cqrs.util.Functions.doIfPresent;
import static com.za.cqrs.util.Functions.mapBy;

@Slf4j
@Component
public class BaseWaitingIssuanceBusinessHandler {

    @Autowired
    private WaitingInsuranceService waitingInsuranceService;

    public Result onStop(IssuanceResult param) {
        List<IssuanceBusinessResult.IssuanceResponse> data = Lists.newArrayList();
        doIfPresent(param.getCreateResponses(), res -> data.addAll(mapBy(param.getCreateResponses(), x -> IssuanceBusinessResult.map(x))));
        doIfPresent(param.getChangeResponses(), res -> data.addAll(mapBy(param.getChangeResponses(), x -> IssuanceBusinessResult.map(x))));
        return IssuanceBusinessResult.builder().data(data).build();
    }

    @Transactional
    public void handle(PolicyPhase phase, BasicUiModel uiModel, IssuanceResult param, BusinessHandleContext context) {
        if (phase == PolicyPhase.waitingInsurance) {
            IssuanceResult result = waitingInsuranceService.handle(context);
            BeanUtils.copyProperties(result, param);
        }
    }

}
