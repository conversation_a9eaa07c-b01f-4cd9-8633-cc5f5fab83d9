/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.dsl.DataDataProvideDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.dsl.DataProvideMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.ExecuteBaseResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.IExecutorRunner;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoLiabilities;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProduct;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProducts;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.Liability;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.QuotationErrorCode;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.benefitoption.result.BenefitOptionComposition;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.benefitoption.result.BenefitOptionsResult;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/25 14:48
 **/
@Component
@Slf4j
public class QuoteCheckService {

    @Autowired
    IExecutorRunner executorRunner;

    public void check(String orderNo, BasicUiModel uiModel) {
        doCheckAccidentBenefit(orderNo, uiModel);
    }

    private void doCheckAccidentBenefit(String orderNo, BasicUiModel basicUiModel) {
        AutoProducts products = basicUiModel.getProducts();
        AutoProduct accident = products.getAccident();
        if (accident == null) {
            return;
        }
        if (!Objects.equals(true, accident.getSelected())) {
            return;
        }
        // 存储liabilityId和sumInsured的映射
        Map<Long, String> liabilityMap = new HashMap<>();

        AutoLiabilities liabilities = accident.getLiabilities();
        if (liabilities == null) {
            return;
        }

        var request = new DataDataProvideDslTemplate();
        request.getMetadata()
            .setProviders(List.of(
                new DataProvideMetaInfo.Provider().setName("productBenefitOptions").setProductCategory(FreeMartProductCategoryEnum.AUTO)));
        var results = executorRunner.runDataProviders(orderNo, request);
        BenefitOptionsResult benefitOptionsResult = (BenefitOptionsResult) results.headOption().map(ExecuteBaseResult::getOriginalResultIfSucceed).orElse(null);

        if (benefitOptionsResult == null) {
            log.info("QuoteCheckService#checkAccidentBenefit benefitOptionsResult is null");
            return;
        }
        //两个责任选一个，匹配即可
        Liability driverPassengerDeath = liabilities.getDriverPassengerDeathInVehicleAccident();
        if (driverPassengerDeath != null) {
            liabilityMap.put(driverPassengerDeath.getLiabilityId(), driverPassengerDeath.getSumInsured());
        }

        BenefitOptionComposition benefitOptionComposition = benefitOptionsResult.getOptionComposition().get("accident");
        if (benefitOptionComposition != null) {
            List<BenefitOptionComposition.BenefitOptionCompositionCodeToItem> items = benefitOptionComposition.getBenefitOptionCompositionItems();

            boolean isMatch = items.stream()
                .flatMap(item -> item.getBenefitOptionCompositionItems().stream())
                .anyMatch(option -> Objects.equals(option.getSumInsured(), liabilityMap.get(option.getLiabilityId())));

            if (!isMatch) {
                throw CommonException.byError(QuotationErrorCode.ACCIDENT_SA_RATABLE_NOT_MATCH);
            }
        }
    }


}
