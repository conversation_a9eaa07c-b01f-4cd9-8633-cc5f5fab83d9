/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.lifecycle.context.OrderLifecycleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.issuance.IssuanceService;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.IssuanceCheckService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE;

@Slf4j
@Component
public class UpdateWaitingInsuranceService {

    @Autowired
    private IssuanceService issuanceService;

    @Autowired
    private UIModelServiceFactory uiModelServiceFactory;

    @Autowired
    private Converters converters;

    @Autowired
    private IssuanceCheckService issuanceCheckService;

    public List<IssuanceResponse> updateByUiModelData(UIModelOrderOutput<BasicUiModel> orderOutput, OrderLifecycleContext context) {
        log.info("UpdateWaitingInsuranceService.updateByUiModel orderId:{}", orderOutput.getOrderId());

        issuanceCheckService.checkIfLockByUser(new ContextHolder(orderOutput));
        ContextHolder contextHolder = new ContextHolder(orderOutput, context.getUserInfoOpt().orElse(null));
        List<IssuanceRequest> requestList = contextHolder.getOrderContextHolders()
            .stream()
            .filter(e -> e.getPolicyRecord().isPresent())
            //是暂存单的情况下才能更新
            .filter(e -> {
                PolicyRecordInfoOutput policyRecordInfoOutput = e.getPolicyRecord().get();
                boolean tempInsurance = policyRecordInfoOutput.isTempInsurance();
                if (!tempInsurance) {
                    log.info("Skip the update, issuance(:{}) is not WAITING_FOR_CREATEISSUANCE", policyRecordInfoOutput.getIssuanceNo());
                }
                return tempInsurance;
            })
            .peek(e -> e.getIssuanceNo().ifPresent(issuanceCheckService::checkStatus))
            .map(p -> {
                AutoConvertIssuanceRequestContext issuanceContext = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(p.getOrderContext(), TraceOp.getTenant()));
                PolicyRecordInfoOutput record = p.getPolicyRecord().orElse(null);
                issuanceContext.setSelectedGoodsId(record.getGoodsId());
                //TODO 后续优化，不需要传递过去
                issuanceContext.setPhase(PolicyPhase.waitingInsurance);
                IssuanceRequest issuanceRequest = new IssuanceRequest();
                BasicUiModel basicUiModel =
                    uiModelServiceFactory.getQueryService().<BasicUiModel>query(p.getOrderId()).getDataOpt().orElse(orderOutput.getDataOpt().orElse(null));
                if (Objects.nonNull(basicUiModel)) {
                    issuanceContext.setSelectedGoods(basicUiModel.selectedGoods(record.getGoodsId()));
                }
                BusinessHandleContext businessHandleContext = issuanceContext.getBusinessHandleContext();
                context.getUserInfoOpt().ifPresent(businessHandleContext::setUserInfo);
                issuanceRequest = converters.convert(basicUiModel, issuanceRequest, issuanceContext);
                issuanceRequest.setIssuanceTransactionType(WAITING_FOR_CREATEISSUANCE);
                issuanceRequest.setIssuanceNo(record.getIssuanceNo());
                return issuanceRequest;
            }).collect(Collectors.toList());
        return issuanceService.batchUpdate(requestList);
    }

}
