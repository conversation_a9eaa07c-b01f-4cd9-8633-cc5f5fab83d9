/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.masterpolicy.apply;

import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.masterpolicy.MasterPolicyService;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.masterpolicy.result.MasterPolicyApplyResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE;

@Slf4j
@Component
public class BaseApplyBusinessHandler {

    @Autowired
    private MasterPolicyService masterPolicyService;

    @Autowired
    private Converters converters;

    @Transactional
    public Result onStop(BasicUiModel uiModel, MasterPolicyApplyResult applyResult, BusinessHandleContext context) {
        return applyResult;
    }

    @Transactional
    public void handle(PolicyPhase phase, BasicUiModel uiModel, MasterPolicyApplyResult applyResult, BusinessHandleContext context) {
        log.info("Master policy order : {} apply plan", context.getOrder().getOrderNo());
        ContextHolder contextHolder = new ContextHolder(context.getOrder());
        List<IssuanceRequest> requests = uiModel.selectedGoodsId()
            .stream()
            .map(e -> {
                AutoGoods autoGoods = uiModel.selectedGoods(e);
                AutoConvertIssuanceRequestContext issuanceRequestContext = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(contextHolder.getOrderContext(), TraceOp.getTenant()));
                issuanceRequestContext.setSelectedGoodsId(autoGoods.getGoodsId());
                issuanceRequestContext.setPhase(phase);
                IssuanceRequest issuanceRequest = new IssuanceRequest();
                issuanceRequest = converters.convert(uiModel, issuanceRequest, issuanceRequestContext);
                issuanceRequest.setIssuanceTransactionType(UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE);
                return issuanceRequest;
            }).collect(Collectors.toList());

        masterPolicyService.applyPlan(requests);
    }

}
