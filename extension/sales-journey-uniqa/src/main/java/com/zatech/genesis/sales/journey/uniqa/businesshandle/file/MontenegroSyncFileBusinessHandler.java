/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.file;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Executor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/8/13 14:08
 **/
@Slf4j
@UIModelBusinessHandler(
    name = "syncFile",
    kind = BusinessHandlerKind.policy,
    desc = "sync file to policy",
    fillParamMode = Executor.FillParamMode.specParam,
    tags = Consts.MONTENEGRO)
public class MontenegroSyncFileBusinessHandler extends AbstractSyncFileBusinessHandler {

}
