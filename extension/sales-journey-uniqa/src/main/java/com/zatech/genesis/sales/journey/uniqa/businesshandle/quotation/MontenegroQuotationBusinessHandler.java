/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.param.QuotationParam;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.Liability;
import com.zatech.genesis.sales.journey.uniqa.uimodel.montenegro.MontenegroUiModel;

import java.util.List;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @date 2024.04.25
 */
@Slf4j
@UIModelBusinessHandler(name = "quotation", kind = BusinessHandlerKind.market, desc = "Montenegro auto quotation business handler", tags = Consts.MONTENEGRO)
public class MontenegroQuotationBusinessHandler extends BaseQuotationBusinessHandler implements IAuthBusinessHandler<MarketPhase, MontenegroUiModel, QuotationParam> {

    public static final String MTPL_DATA = "{\n" +
        "    \"goodsCode\": \"MTPL\",\n" +
        "    \"goodsId\": 1217295619735552,\n" +
        "    \"selected\": true,\n" +
        "    \"isRenewalPolicy\": false,\n" +
        "    \"plan\": {\n" +
        "      \"planId\": 1217324174557184,\n" +
        "      \"premiumFrequencyType\": \"SINGLE\",\n" +
        "      \"premiumPeriodType\": \"YEARFULL\",\n" +
        "      \"premiumPeriod\": \"1\",\n" +
        "      \"coveragePeriodType\": \"DAY\",\n" +
        "      \"coveragePeriod\": \"365\",\n" +
        "      \"saCurrency\": \"EUR\",\n" +
        "      \"premiumCurrency\": \"EUR\",\n" +
        "      \"elements\": {\n" +
        "        \"nonStandardTariff\": false\n" +
        "      }\n" +
        "    },\n" +
        "    \"elements\": {\n" +
        "      \"E_Exposure\": \"0\",\n" +
        "      \"E_Frequency\": \"0\"\n" +
        "    }\n" +
        "  }\n";

    @Value(value = "${zatech.sales-journey.only.mhull.enabled:false}")
    private boolean onlyMhull;


    @Override
    public MarketPhase[] supportedPhases() {
        return new MarketPhase[] {MarketPhase.quotation, MarketPhase.quickQuotation, MarketPhase.previewQuotation};
    }

    @Override
    public Result onContinueError(Exception e, MarketPhase marketPhase, MontenegroUiModel montenegroUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.onContinueError(e, marketPhase, montenegroUiModel, quotationParam, businessHandleContext);
    }

    @Override
    public Result onContinueSucceed(Result result, MarketPhase marketPhase, MontenegroUiModel montenegroUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.onContinueSucceed(result, marketPhase, montenegroUiModel, quotationParam, businessHandleContext);
    }

    @Override
    public Result onStop(MarketPhase marketPhase, MontenegroUiModel montenegroUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        return super.mockResult();
    }

    @Override
    public FlowStrategy handle(MarketPhase marketPhase, MontenegroUiModel montenegroUiModel, QuotationParam quotationParam, BusinessHandleContext businessHandleContext) {
        if (marketPhase.equals(MarketPhase.quickQuotation) && onlyMhull) {
            AutoGoods trafficCompulsoryInsuranceGoods = StaticJsonParser.fromJsonString(MTPL_DATA, AutoGoods.class);
            List<Liability> liabilities = Optional.ofNullable(montenegroUiModel.getProducts().getTrafficCompulsoryInsuranceMainProduct())
                .map(autoProduct -> autoProduct.getLiabilities().getSelectedLiabilities()).orElse(null);
            if (CollectionUtils.isNotEmpty(liabilities)) {
                montenegroUiModel.setTrafficCompulsoryInsuranceGoods(trafficCompulsoryInsuranceGoods);
            }
        }
        super.handle(marketPhase, montenegroUiModel, quotationParam, businessHandleContext);
        return FlowStrategy.Continue;
    }

}
