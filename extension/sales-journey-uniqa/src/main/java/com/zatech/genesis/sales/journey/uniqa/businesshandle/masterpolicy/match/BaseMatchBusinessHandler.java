/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.masterpolicy.match;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.zatech.gaia.resource.biz.NcdCalculationBasisEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.MasterAgreementDeductibleConstraintTypeEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.MasterAgreementPaymentFrequencyEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.MasterAgreementPremiumConstraintTypeEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.MasterAgreementSaleConstraintTypeEnum;
import com.zatech.gaia.resource.components.enums.policymanagement.MasterAgreementSumAssuredConstraintTypeEnum;
import com.zatech.gaia.resource.components.enums.product.AdditionalTypeEnum;
import com.zatech.gaia.resource.components.enums.product.LoadingMethodEnum;
import com.zatech.gaia.resource.components.enums.product.PayFrequencyTypeEnum;
import com.zatech.gaia.resource.components.enums.product.PremiumDiscountTypeEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.management.api.base.agreement.MasterAgreementBasicInfo;
import com.zatech.genesis.policy.management.api.base.agreement.MasterAgreementExtendInfo;
import com.zatech.genesis.policy.management.api.enums.AdjustedFeeTypeEnum;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementAdjustedFeeResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageProductInfoResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageProductLiabilityInfoResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementSpecialAgreementResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MatchPlanResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.share.EnumHelper;
import com.zatech.genesis.product.api.structure.request.QueryProductListRequest;
import com.zatech.genesis.product.api.structure.response.ProductResponse;
import com.zatech.genesis.product.api.structure.response.agreement.liabilitylevel.LiabilityCombinePremiumDiscountResponse;
import com.zatech.genesis.product.api.structure.response.agreement.liabilitylevel.LiabilityPremiumDiscountItemResponse;
import com.zatech.genesis.product.api.structure.response.agreement.productlevel.PremiumDiscountItemResponse;
import com.zatech.genesis.sales.journey.client.biz.auto.dataprovide.feeadjustmentconfig.service.config.BizDateService;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.masterpolicy.MasterPolicyService;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.masterpolicy.result.MasterPolicyMatchResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPolicyElements;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProduct;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.Elements;
import com.zatech.genesis.sales.journey.client.biz.common.model.Goods;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.CampaignItem;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.Liability;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.PremiumDiscount;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.product.Surcharge;
import com.zatech.genesis.sales.journey.integration.product.outer.OuterProductService;
import com.zatech.genesis.sales.journey.plugin.api.enums.TrailStepEnum;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.masterpolicy.MasterPolicyConvertContext;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.masterpolicy.match.result.MasterPolicyMatchBusinessResult;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.benefitoption.result.BenefitOptionCompositionItem;
import com.zatech.genesis.sales.journey.uniqa.dataprovider.benefitoption.service.BenefitOptionsService;
import com.zatech.genesis.sales.journey.uniqa.diff.DIffHandler;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.constant.SystemSourceConstants;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.BooleanToYesNoEnum;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.StringToBigDecimal;
import com.zatech.genesis.sales.journey.uniqa.units.ReflectorsUnits;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.za.cqrs.util.Functions.doIfPresent;
import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE;
import static java.util.Optional.ofNullable;

@Slf4j
@Component
public class BaseMatchBusinessHandler {

    @Autowired
    private MasterPolicyService masterPolicyService;

    @Autowired
    private Converters converters;

    @Autowired
    private DIffHandler dIffHandler;

    @Autowired
    private OuterProductService productService;

    @Autowired
    private BenefitOptionsService benefitOptionsService;

    @Autowired
    private BizDateService bizDateService;

    @Transactional
    public Result onStop(BasicUiModel uiModel, MasterPolicyMatchResult matchResult, BusinessHandleContext context) {
        MatchPlanResponse matchPlanResponse = matchResult.getResponse();
        var builder = MasterPolicyMatchBusinessResult.builder();
        BasicUiModel initUiModel = dIffHandler.createUiModel(new ContextHolder(context.getOrder()));
        resetUiModel(initUiModel, uiModel);

        if (matchPlanResponse == null || CollectionUtils.isEmpty(matchPlanResponse.getPlanCoverageList())) {
            return builder.basicUiModel(uiModel).originalBasicUiModel(initUiModel).build();
        }

        convertBasicInfo(initUiModel, true, matchPlanResponse.getMasterAgreementBasicInfo(), context);
        convertBasicInfo(uiModel, false, matchPlanResponse.getMasterAgreementBasicInfo(), context);

        Map<Long, MasterAgreementPlanCoverageResponse> goodsMap = matchPlanResponse.getPlanCoverageList().stream()
            .collect(Collectors.toMap(MasterAgreementPlanCoverageResponse::getGoodsId, Function.identity()));

        Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap = Maps.newHashMap();
        Map<Long, MasterAgreementPlanCoverageProductLiabilityInfoResponse> liabilityMap = Maps.newHashMap();

        matchPlanResponse.getPlanCoverageList().forEach(plan ->
            plan.getProductInfoList()
                .stream()
                .filter(product -> !Objects.equals(product.getSaleConstraintType(), MasterAgreementSaleConstraintTypeEnum.PROHIBIT))
                .forEach(product -> {
                    productMap.put(Pair.of(plan.getGoodsId(), product.getProductId()), product);

                    product.getLiabilityInfoList()
                        .stream()
                        .filter(liability -> !Objects.equals(liability.getSaleConstraintType(), MasterAgreementSaleConstraintTypeEnum.PROHIBIT))
                        .forEach(liability -> liabilityMap.put(liability.getLiabilityId(), liability));
                }));
        Set<Long> productList = productMap.keySet().stream().map(Pair::getRight).collect(Collectors.toSet());
        Map<Long, ProductResponse> productResponseMap = queryProduct(productList).stream().collect(Collectors.toMap(ProductResponse::getProductId, Function.identity()));

        convertGoods(new MasterPolicyConvertContext(initUiModel, true, goodsMap, productMap, liabilityMap, matchPlanResponse.getMasterAgreementExtendInfo(), context.getOrder()));
        convertGoods(new MasterPolicyConvertContext(uiModel, false, goodsMap, productMap, liabilityMap, matchPlanResponse.getMasterAgreementExtendInfo(), context.getOrder()));
        convertAdjustFee(initUiModel, matchPlanResponse.getPlanCoverageList(), productResponseMap, productMap);
        convertAdjustFee(uiModel, matchPlanResponse.getPlanCoverageList(), productResponseMap, productMap);

        return builder.basicUiModel(uiModel).originalBasicUiModel(initUiModel).build();
    }

    private void convertBasicInfo(BasicUiModel uiModel, boolean isInitModel, MasterAgreementBasicInfo basicInfo, BusinessHandleContext context) {
        if (basicInfo == null) {
            return;
        }
        TrailStepEnum stepType = context.getTrailOpt()
            .map(trailContext -> TrailStepEnum.valueOf(trailContext.getStep()))
            .orElse(null);

        uiModel.setIssueWithoutPayment(BooleanToYesNoEnum.convert(basicInfo.getIssueWithoutPaymentNormalPolicy()));
        Optional.ofNullable(basicInfo.getPaymentFrequency()).ifPresent(
            d -> {
                // 为 origin 无脑塞 或者 （正向流程 且 basicInfo页面）
                if (isInitModel || (!isReverseProcess(uiModel) && stepType == TrailStepEnum.BASIC_INFO)) {
                    uiModel.getAllGoods().stream()
                        .filter(Objects::nonNull)
                        .filter(goods -> Objects.nonNull(goods.getPlan()))
                        .forEach(goods -> goods.getPlan().setPremiumFrequencyType(toPayFrequencyType(d)));
                }
            }
        );
    }

    /**
     * 反向流程
     *
     * @param uiModel
     * @return
     */
    private boolean isReverseProcess(BasicUiModel uiModel) {
        return uiModel.getMasterPolicyNo() != null && SystemSourceConstants.GRAPHENE.equals(uiModel.getSystemSource());
    }

    public PayFrequencyTypeEnum toPayFrequencyType(MasterAgreementPaymentFrequencyEnum paymentFrequencyEnum) {
        return switch (paymentFrequencyEnum) {
            case SINGLE_PAYMENT -> PayFrequencyTypeEnum.SINGLE;
            case MONTHLY -> PayFrequencyTypeEnum.MONTH;
            case QUARTERLY -> PayFrequencyTypeEnum.QUARTER;
            case SEMI_YEARLY -> PayFrequencyTypeEnum.HALFYEAR;
            case YEARLY -> PayFrequencyTypeEnum.YEAR;
        };
    }

    @Transactional
    public void handle(PolicyPhase phase, BasicUiModel uiModel, MasterPolicyMatchResult
        matchResult, BusinessHandleContext context) {
        log.info("Master policy order : {} match plan", context.getOrder().getOrderNo());
        ContextHolder contextHolder = new ContextHolder(context.getOrder());
        List<IssuanceRequest> requests = uiModel.selectedGoodsId()
            .stream()
            .map(e -> {
                AutoGoods autoGoods = uiModel.selectedGoods(e);
                AutoConvertIssuanceRequestContext issuanceRequestContext = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(contextHolder.getOrderContext(), TraceOp.getTenant()));
                issuanceRequestContext.setSelectedGoodsId(autoGoods.getGoodsId());
                issuanceRequestContext.setPhase(phase);
                issuanceRequestContext.setMatchMasterPolicy(Boolean.TRUE);
                IssuanceRequest issuanceRequest = new IssuanceRequest();
                issuanceRequest = converters.convert(uiModel, issuanceRequest, issuanceRequestContext);
                issuanceRequest.setIssuanceTransactionType(UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE);
                return issuanceRequest;
            }).collect(Collectors.toList());

        MasterPolicyMatchResult response = masterPolicyService.matchPlan(requests);
        MatchPlanResponse matchPlanResponse = response.getResponse();
        List<MasterAgreementPlanCoverageResponse> planCoverageList = matchPlanResponse.getPlanCoverageList();
        if (ScenarioTypeEnum.renew.equals(uiModel.getScenario())) {
            uiModel.selectedGoodsId().stream().findFirst().ifPresent(selectedGoodsId -> {
                List<MasterAgreementPlanCoverageResponse> filteredPlanCoverageList = planCoverageList.stream()
                    .filter(coverage -> selectedGoodsId.equals(coverage.getGoodsId()))
                    .collect(Collectors.toList());
                matchPlanResponse.setPlanCoverageList(filteredPlanCoverageList);
            });
        }
        BeanUtils.copyProperties(response, matchResult);
        matchResult.setRequests(requests);
    }

    private void convertGoods(MasterPolicyConvertContext context) {

        setGoodsSelection(context);
        BasicUiModel uiModel = context.getUiModel();
        val reflectorsUnits = new ReflectorsUnits(uiModel);
        val productIds = uiModel.getAllProducts().stream()
            .map(AutoProduct::getProductId)
            .toList();

        Date bizDate = bizDateService.getBizDate(context.getUiModel().getScenario(), new Date(), context.getOrderContext());
        Map<Long, Map<String, List<BenefitOptionCompositionItem>>> productIdBenefitOptionCompositionResponseMap =
            benefitOptionsService.getProductIdBenefitOptionCompositionResponseMap(productIds, reflectorsUnits, bizDate);

        Map<String, List<BenefitOptionCompositionItem>> benefitOptionCompositionItemMap = new HashMap<>();

        uiModel.getAllProducts().forEach(p -> handleProduct(p, context, productIdBenefitOptionCompositionResponseMap, benefitOptionCompositionItemMap));

        Optional<MasterAgreementPlanCoverageProductInfoResponse> deductibleConstraintValue = context.getProductMap().values().stream().filter(data -> data.getDeductibleConstraintType() == MasterAgreementDeductibleConstraintTypeEnum.COMPOSITE).findFirst();

        Optional.ofNullable(context.getUiModel().getCommercialInsuranceGoods()).ifPresent(
            autoGoods -> {
                AutoPolicyElements elements = ofNullable(autoGoods.getElements()).orElse(new AutoPolicyElements());
                elements.setIsDefaultForMasterPlanClaimStack(true);
                autoGoods.setElements(elements);
            }
        );
        if (deductibleConstraintValue.isPresent()) {
            applyDeductibleConstraints(context.getUiModel(), deductibleConstraintValue.orElseThrow());
        }

        applyStandardTariff(uiModel);

        applyBenefitOptions(uiModel, benefitOptionCompositionItemMap);
    }

    private void setGoodsSelection(MasterPolicyConvertContext context) {
        Set<Long> matchGoodsIds = context.getGoodsMap().keySet();
        List<Long> uiModelGoodsIds = context.getUiModel().getAllGoods()
            .stream()
            .map(Goods::getGoodsId)
            .toList();
        Collection<Long> intersection = CollectionUtils.intersection(matchGoodsIds, uiModelGoodsIds);
        context.getUiModel().getAllGoods().forEach(g -> {
            if (intersection.contains(g.getGoodsId())) {
                g.setSelected(context.getUiModel().selectedGoods(g.getGoodsId()).getSelected());
            } else {
                context.getUiModel().removeGoods(g.getGoodsId());
            }
        });

        MasterAgreementExtendInfo masterAgreementExtendInfo = context.getMasterAgreementExtendInfo();
        if (masterAgreementExtendInfo != null) {
            doIfPresent(masterAgreementExtendInfo.getPublicTender(), publicTender -> context.getUiModel().setPublicTender(BooleanToYesNoEnum.convert(publicTender)));
            doIfPresent(masterAgreementExtendInfo.getPublicTenderNo(), context.getUiModel()::setPublicTenderNo);
            doIfPresent(masterAgreementExtendInfo.getExtraPremiumDueDate(), context.getUiModel()::setExtraPremiumDueDays);
        }
    }

    private void handleProduct(AutoProduct product,
                               MasterPolicyConvertContext context,
                               Map<Long, Map<String, List<BenefitOptionCompositionItem>>> productIdBenefitOptionCompositionResponseMap,
                               Map<String, List<BenefitOptionCompositionItem>> benefitOptionCompositionItemMap) {
        if (product.getLiabilities() == null) {
            return;
        }
        MasterAgreementPlanCoverageProductInfoResponse productInfoResponse = null;
        for (Map.Entry<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> entry : context.getProductMap().entrySet()) {
            if (Objects.equals(entry.getKey().getRight(), product.getProductId())) {
                productInfoResponse = entry.getValue();
            }
        }

        if (productInfoResponse != null) {
            if (context.isInitModel()) {
                product.setSelected(toSelect(productInfoResponse.getSaleConstraintType()));
            }
            if (context.isInitModel() || (productInfoResponse.getSumAssuredConstraintType() == MasterAgreementSumAssuredConstraintTypeEnum.AMOUNT && StringUtils.isNotBlank(productInfoResponse.getSumAssuredConstraintValue()))) {
                //isInitModel ： true，不管有没有都塞
                product.setSumInsured(StringToBigDecimal.convert(productInfoResponse.getSumAssuredConstraintValue()));
            }
            //设置保费
            applyAdjustedAnnualStandardPremium(product, productInfoResponse);

            handleLiabilities(product, context, productIdBenefitOptionCompositionResponseMap, benefitOptionCompositionItemMap, productInfoResponse);
        } else {
            product.setSelected(null);
            product.getLiabilities().getAllLiabilities().forEach(l -> l.setSelected(null));
        }
    }

    /**
     * 设置保费
     *
     * @param product
     * @param productInfoResponse
     */
    private void applyAdjustedAnnualStandardPremium(AutoProduct product, MasterAgreementPlanCoverageProductInfoResponse productInfoResponse) {
        MasterAgreementPremiumConstraintTypeEnum constraintType = productInfoResponse.getPremiumConstraintType();
        Optional.ofNullable(constraintType).ifPresent(
            type -> {
                switch (constraintType) {
                    case AMOUNT -> product.setAdjustedAnnualStandardPremium(StringToBigDecimal.convert(productInfoResponse.getPremiumConstraintValue()));
                    case RATE -> product.setAdjustedAnnualStandardPremiumRate(StringToBigDecimal.convert(productInfoResponse.getPremiumConstraintValue()));
                    default -> log.info("constraintType value not match : {}", constraintType);
                }
            }
        );
    }

    /**
     * 设置是否是非标准保费
     *
     * @param initUiModel
     */
    private void applyStandardTariff(BasicUiModel initUiModel) {
        boolean hasNonStandardPremiumProduct = initUiModel.getAllProducts().stream()
            .filter(p -> p.getSelected() != null)
            .anyMatch(product -> product.getAdjustedAnnualStandardPremium() != null || product.getAdjustedAnnualStandardPremiumRate() != null);

        initUiModel.getAllGoods().stream()
            .filter(g -> g.getSelected() != null)
            .forEach(autoGoods -> {
                var elements = Objects.requireNonNullElseGet(autoGoods.getPlan().getElements(), Elements::new);
                elements.setNonStandardTariff(hasNonStandardPremiumProduct);
                autoGoods.setElements(elements);
            });
    }

    /**
     * Deductible 信息设置在UIModel plan 上的 claimStack
     *
     * @param initUiModel
     * @param productInfoResponse
     */
    private void applyDeductibleConstraints(BasicUiModel initUiModel, MasterAgreementPlanCoverageProductInfoResponse productInfoResponse) {
        Optional.ofNullable(initUiModel.getCommercialInsuranceGoods()).ifPresent(
            autoGoods -> {
                autoGoods.getPlan().getClaimStack().put("MotorHullDeductibleAmount", productInfoResponse.getDeductibleConstraintValue().getAmount());
                autoGoods.getPlan().getClaimStack().put("MotorHullDeductiblePercentage", new BigDecimal(productInfoResponse.getDeductibleConstraintValue().getRate())
                    .multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP).toString());
                AutoPolicyElements elements = ofNullable(autoGoods.getElements()).orElse(new AutoPolicyElements());
                elements.setIsDefaultForMasterPlanClaimStack(false);
                autoGoods.setElements(elements);
            }
        );
    }

    private void handleLiabilities(AutoProduct product,
                                   MasterPolicyConvertContext context,
                                   Map<Long, Map<String, List<BenefitOptionCompositionItem>>> productIdBenefitOptionCompositionResponseMap,
                                   Map<String, List<BenefitOptionCompositionItem>> benefitOptionCompositionItemMap,
                                   MasterAgreementPlanCoverageProductInfoResponse productInfoResponse) {
        AutoProduct copiedProduct = JsonParser.copyObject(product, AutoProduct.class);
        product.getLiabilities().getAllLiabilities().forEach(liability -> {
            Map<Long, MasterAgreementPlanCoverageProductLiabilityInfoResponse> liabilityMap = context.getLiabilityMap();
            if (liabilityMap.containsKey(liability.getLiabilityId())) {
                MasterAgreementPlanCoverageProductLiabilityInfoResponse liabilityInfoResponse = liabilityMap.get(liability.getLiabilityId());
                if (context.isInitModel()) {
                    liability.setSelected(toSelect(liabilityInfoResponse.getSaleConstraintType()));
                }

                if (context.isInitModel() || (liabilityInfoResponse.getSumAssuredConstraintType() == MasterAgreementSumAssuredConstraintTypeEnum.AMOUNT
                    && StringUtils.isNotBlank(liabilityInfoResponse.getSumAssuredConstraintValue()))) {
                    liability.setSumInsured(liabilityInfoResponse.getSumAssuredConstraintValue());
                }
                if (liabilityInfoResponse.getSumAssuredConstraintValue() != null) {
                    //再根据金额和责任判断来塞联合定价
                    benefitOptionCompositionItemMap.putAll(filterBenefitOption(productIdBenefitOptionCompositionResponseMap, liabilityInfoResponse.getLiabilityId(), liabilityInfoResponse.getSumAssuredConstraintValue()));
                }

            } else {
                liability.setSelected(null);
            }
        });

        //如果master plan配置中一个product下所有的责任都是optional的，这个时候需要先根据uimodel设置其中默认选中的责任，如果uimodel中默认选中的责任被禁掉了，那么就直接保留现在的逻辑，取master plan中第一个
        if (!Objects.equals(Boolean.TRUE, product.getMainProduct())) {
            Liability liability = product.getLiabilities().getAllLiabilities().stream().filter(l -> l.getSelected() != null && l.getSelected()).findFirst().orElse(null);
            if (liability == null) {
                List<Liability> initSelectedLiabilityList = copiedProduct.getLiabilities().getSelectedLiabilities().stream().toList();
                if (initDataHaveMasterPlanLiability(product, initSelectedLiabilityList)) {
                    initSelectedLiabilityList
                        .forEach(x -> product.getLiabilities().getAllLiabilities()
                            .stream()
                            .filter(l -> Objects.equals(l.getLiabilityId(), x.getLiabilityId()))
                            .forEach(l -> l.setSelected(true)));
                } else {
                    Long liabilityId = productInfoResponse.getLiabilityInfoList().get(0).getLiabilityId();
                    product.getLiabilities().getAllLiabilities().stream()
                        .filter(l -> Objects.equals(l.getLiabilityId(), liabilityId))
                        .forEach(l -> l.setSelected(true));
                }
            }
        }
    }

    private static boolean initDataHaveMasterPlanLiability(AutoProduct product, List<Liability> initSelectedLiabilityList) {
        return !CollectionUtils.isEmpty(initSelectedLiabilityList) && initSelectedLiabilityList.stream().anyMatch(l -> product.getLiabilities().getAllLiabilities().stream().filter(Objects::nonNull).filter(la -> Objects.nonNull(la.getSelected())).anyMatch(la -> Objects.equals(la.getLiabilityId(), l.getLiabilityId())));
    }

    /**
     * 根据liabilityId 和 SA 过滤出联合计价的责任和保额
     *
     * @param productIdBenefitOptionCompositionResponseMap
     * @param liabilityId
     * @param sumAssuredConstraintValue
     * @return
     */
    private Map<String, List<BenefitOptionCompositionItem>> filterBenefitOption(Map<Long, Map<String, List<BenefitOptionCompositionItem>>> productIdBenefitOptionCompositionResponseMap, Long liabilityId, String sumAssuredConstraintValue) {
        return productIdBenefitOptionCompositionResponseMap.values().stream()
            .flatMap(innerMap -> innerMap.entrySet().stream())
            .filter(entry -> entry.getValue().stream()
                .anyMatch(item -> item.getLiabilityId().equals(liabilityId) && item.getSumInsured().equals(sumAssuredConstraintValue)))
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }

    private void applyBenefitOptions(BasicUiModel initUiModel, Map<String, List<BenefitOptionCompositionItem>> benefitOptionCompositionItemMap) {
        Optional.ofNullable(benefitOptionCompositionItemMap).ifPresent(item ->
            item.forEach((key, valueList) -> valueList.forEach(value ->
                initUiModel.getAllProducts().forEach(p -> {
                    if (p.getLiabilities() == null) {
                        return;
                    }
                    p.getLiabilities().getSelectedLiabilities().forEach(liability -> {
                        if (liability.getLiabilityId().equals(value.getLiabilityId())) {
                            liability.setSumInsured(value.getSumInsured());
                        }
                    });
                })
            ))
        );
    }

    private void convertAdjustFee(BasicUiModel initUiModel, List<MasterAgreementPlanCoverageResponse> planCoverageList, Map<Long, ProductResponse> productResponseMap,
                                  Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap) {
        if (CollectionUtils.isEmpty(planCoverageList)) {
            return;
        }
        planCoverageList.stream().filter(plan -> Objects.nonNull(initUiModel.selectedGoods(plan.getGoodsId()))).forEach(plan -> {
            if (!CollectionUtils.isEmpty(plan.getAdjustedFeeList())) {
                convertCampaign(initUiModel, plan);
                convertDiscount(initUiModel, plan, productResponseMap, productMap);
                convertLoading(initUiModel, plan, productResponseMap, productMap);
            }

            List<MasterAgreementSpecialAgreementResponse> specialAgreementList = plan.getSpecialAgreementList();
            if (!CollectionUtils.isEmpty(specialAgreementList)) {
                convertSpecialCondition(initUiModel, specialAgreementList, plan.getGoodsId());
            }

        });
    }

    /**
     * 转换配置上的Special Agreement
     *
     * @param initUiModel
     * @param specialAgreementList
     * @param goodsId
     */
    private void convertSpecialCondition(BasicUiModel initUiModel, List<MasterAgreementSpecialAgreementResponse> specialAgreementList, Long goodsId) {
        String specialAgreementDesc = specialAgreementList.stream().map(MasterAgreementSpecialAgreementResponse::getSpecialAgreementDescription).collect(Collectors.joining(","));
        AutoPolicyElements elements = Optional.ofNullable(initUiModel.selectedGoods(goodsId).getElements()).orElse(new AutoPolicyElements());
        elements.setSpecialConditions(specialAgreementDesc);
        initUiModel.selectedGoods(goodsId).setElements(elements);
    }

    private static void convertLoading(BasicUiModel initUiModel, MasterAgreementPlanCoverageResponse plan, Map<Long, ProductResponse> productResponseMap,
                                       Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap) {
        List<MasterAgreementAdjustedFeeResponse> loadingList = plan.getAdjustedFeeList().stream().filter(a -> AdjustedFeeTypeEnum.LOADING == a.getFeeType()).collect(Collectors.toList());
        for (AutoProduct product : initUiModel.getAllProducts()) {
            ProductResponse productResponse = productResponseMap.get(product.getProductId());
            Pair<Long, Long> pair = Pair.of(plan.getGoodsId(), product.getProductId());
            if (productResponse == null || productResponse.getLiabilityLevelAgreements() == null || product.getLiabilities() == null
                || CollectionUtils.isEmpty(productResponse.getLiabilityLevelAgreements().getConditionalUnderwritingResponseList())
                || !productMap.containsKey(pair)) {
                continue;
            }

            product.getLiabilities()
                .getAllLiabilities()
                .stream()
                .forEach(liability -> {
                    Set<Surcharge> addSurchargeList = Sets.newHashSet();
                    productResponse.getLiabilityLevelAgreements().getConditionalUnderwritingResponseList()
                        .forEach(uw -> {
                            if (Objects.equals(uw.getLiabilityId(), liability.getLiabilityId())) {
                                Set<Surcharge> surchargeList = loadingList
                                    .stream()
                                    .map(lod -> {
                                        AdditionalTypeEnum loadingType = EnumHelper.nameOf(lod.getFeeName(), AdditionalTypeEnum.class);
                                        if (uw.getExtraLoadingType().equals(loadingType.getCode()) && uw.getCalculationBasis().equals(lod.getAdjustedType().getCode())) {
                                            Surcharge surcharge = new Surcharge();
                                            surcharge.setSurchargeExtraLoadingType(loadingType);
                                            surcharge.setSurchargeLoadingMethod(EnumHelper.codeOf(uw.getLoadingMethod(), LoadingMethodEnum.class));
                                            surcharge.setCalculationBasis(lod.getAdjustedType());
                                            if (lod.getAdjustedType() == NcdCalculationBasisEnum.BY_RATE) {
                                                surcharge.setRate(StringToBigDecimal.convert(lod.getAdjustedValue()));
                                            } else {
                                                surcharge.setAmount(StringToBigDecimal.convert(lod.getAdjustedValue()));
                                            }
                                            return surcharge;
                                        }
                                        return null;
                                    })
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toSet());
                                addSurchargeList.addAll(surchargeList);
                            }
                        });
                    List<Surcharge> orgSurchargeList = Optional.ofNullable(liability.getSurchargeList()).orElse(Lists.newArrayList());
                    addSurchargeList.addAll(orgSurchargeList);
                    liability.setSurchargeList(Lists.newArrayList(addSurchargeList));
                });
        }
    }

    private void convertDiscount(BasicUiModel initUiModel, MasterAgreementPlanCoverageResponse plan, Map<Long, ProductResponse> productResponseMap,
                                 Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap) {
        List<MasterAgreementAdjustedFeeResponse> discountList = plan.getAdjustedFeeList()
            .stream()
            .filter(a -> AdjustedFeeTypeEnum.DISCOUNT == a.getFeeType())
            .collect(Collectors.toList());
        for (AutoProduct product : initUiModel.getAllProducts()) {
            Pair<Long, Long> pair = Pair.of(plan.getGoodsId(), product.getProductId());
            ProductResponse productResponse = productResponseMap.get(product.getProductId());
            if (productResponse == null || productResponse.getLiabilityLevelAgreements() == null || product.getLiabilities() == null
                || CollectionUtils.isEmpty(productResponse.getLiabilityLevelAgreements().getLiabilityPremiumDiscountList())
                || !productMap.containsKey(pair)) {
                continue;
            }

            Map<Long, List<PremiumDiscountItemResponse>> premiumDiscountMap = productResponse.getLiabilityLevelAgreements().getLiabilityPremiumDiscountList()
                .stream()
                .collect(Collectors.toMap(
                    LiabilityPremiumDiscountItemResponse::getLiabilityId,
                    x -> x.getCombineLiabilityGroups().stream().map(LiabilityCombinePremiumDiscountResponse::getPremiumDiscountItemResponseList).flatMap(List::stream).collect(Collectors.toList())
                ));

            product.getLiabilities()
                .getAllLiabilities()
                .stream()
                .forEach(liability -> premiumDiscountMap
                    .forEach((k, v) -> {
                        if (Objects.equals(k, liability.getLiabilityId())) {
                            Map<Integer, PremiumDiscountItemResponse> liabilityPremiumDiscountMap = v.stream()
                                .collect(Collectors.toMap(PremiumDiscountItemResponse::getPremiumDiscountType, Function.identity()));

                            Set<PremiumDiscount> discounts = Sets.newHashSet();
                            discountList.stream().forEach(d -> {
                                PremiumDiscountTypeEnum discountType = EnumHelper.nameOf(d.getFeeName(), PremiumDiscountTypeEnum.class);
                                if (liabilityPremiumDiscountMap.containsKey(discountType.getCode())) {
                                    PremiumDiscount discount = new PremiumDiscount();
                                    discount.setPremiumDiscountType(discountType.getCode());
                                    discount.setRate(d.getAdjustedValue());
                                    discounts.add(discount);
                                }
                            });
                            List<PremiumDiscount> premiumDiscountList = Optional.ofNullable(liability.getPremiumDiscountList()).orElse(Lists.newArrayList());
                            discounts.addAll(premiumDiscountList);
                            liability.setPremiumDiscountList(Lists.newArrayList(discounts));
                        }
                    }));
        }
    }

    private static void convertCampaign(BasicUiModel initUiModel, MasterAgreementPlanCoverageResponse plan) {
        Set<CampaignItem> campaignItems = plan.getAdjustedFeeList().stream()
            .filter(a -> AdjustedFeeTypeEnum.CAMPAIGN == a.getFeeType())
            .map(c -> {
                CampaignItem campaignItem = new CampaignItem();
                campaignItem.setCampaignCode(c.getFeeName());
                if (NcdCalculationBasisEnum.BY_PREMIUM_AMOUNT == c.getAdjustedType()) {
                    campaignItem.setAmount(StringToBigDecimal.convert(c.getAdjustedValue()));
                } else {
                    campaignItem.setRate(StringToBigDecimal.convert(c.getAdjustedValue()));
                }
                return campaignItem;
            }).collect(Collectors.toSet());

        initUiModel.selectedGoodsId().forEach(goodsId -> {
            AutoGoods goods = initUiModel.selectedGoods(goodsId);
            List<CampaignItem> campaignList = Optional.ofNullable(goods.getCampaigns()).orElse(Lists.newArrayList());
            for (CampaignItem campaignItem : campaignList){
                campaignItems.removeIf(campaignItemPlan -> campaignItemPlan.getCampaignCode().equals(campaignItem.getCampaignCode()));
            }
            campaignItems.addAll(campaignList);
            goods.setCampaigns(Lists.newArrayList(campaignItems));
        });
    }

    private List<ProductResponse> queryProduct(Set<Long> productIds) {
        QueryProductListRequest queryProductListRequest = new QueryProductListRequest();
        queryProductListRequest.setProductIds(productIds.stream().toList());
        queryProductListRequest.setQueryLiabilityLevelAgreements(true);
        return productService.queryProductList(queryProductListRequest);
    }

    private Boolean toSelect(MasterAgreementSaleConstraintTypeEnum saleConstraintType) {
        return switch (saleConstraintType) {
            case MANDATORY -> Boolean.TRUE;
            case OPTIONAL -> Boolean.FALSE;
            default -> null;
        };
    }

    private void resetUiModel(BasicUiModel initUiModel, BasicUiModel uiModel) {
        initUiModel.setScenario(uiModel.getScenario());
        initUiModel.setMasterPolicyNo(uiModel.getMasterPolicyNo());
    }

}
