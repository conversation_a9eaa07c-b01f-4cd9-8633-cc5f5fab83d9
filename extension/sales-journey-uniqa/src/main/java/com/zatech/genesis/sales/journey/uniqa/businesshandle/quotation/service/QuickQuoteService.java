/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.portal.toolbox.util.TraceSupport;
import com.zatech.genesis.portal.toolbox.util.scan.IResourceScanner;

import java.io.File;
import java.nio.charset.Charset;

import lombok.SneakyThrows;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import scala.reflect.ClassTag;

@Component
public class QuickQuoteService {

    public static final String QUICK_QUOTE_DEFAULT_DATA = "quick-quote-default-data";

    @Autowired
    private IResourceScanner resourceScanner;

    @Autowired
    private TraceSupport traceSupport;

    private final JsonMap defaultVehicleInfoCache = new JsonMap();

    private final Object cacheLock = new Object();

    public JsonMap loadDefaultVehicleInfo() {
        String fileName = getFileName();
        if (StringUtils.isNotBlank(fileName)) {
            synchronized (cacheLock) {
                return (JsonMap) defaultVehicleInfoCache.computeIfAbsent(getKey(QUICK_QUOTE_DEFAULT_DATA, fileName),
                    k -> resourceScanner.scanAndMap(QUICK_QUOTE_DEFAULT_DATA, fileName, resource -> {
                        if (!resource.exists()) {
                            throw new IllegalArgumentException("can not find file: {}" + resource.getFilename());
                        }
                        if (resource.getFilename() == null || !resource.getFilename().endsWith(".json")) {
                            throw new IllegalArgumentException(resource.getFilename() + "{} must be a json file.");
                        }
                        return getContent(resource);
                    }, ClassTag.apply(JsonMap.class)).headOption().getOrElse(JsonMap::new));
            }
        }
        return new JsonMap();
    }

    private String getFileName() {
        String tenant = traceSupport.getTenant();
        return tenant.concat(".json");
    }

    private String getKey(String folder, String fileName) {
        return folder + File.separator + fileName;
    }

    @SneakyThrows
    private static JsonMap getContent(Resource resource) {
        String content = resource.getContentAsString(Charset.defaultCharset());
        if (StringUtils.isEmpty(content)) {
            return JsonMap.EMPTY;
        } else {
            return JsonParser.fromJsonStringToMap(content);
        }
    }
}
