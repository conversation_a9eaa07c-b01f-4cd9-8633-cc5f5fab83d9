/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result;

import com.zatech.gaia.resource.components.enums.schema.SaPremiumCalculationMethodEnum;

import java.util.List;

import lombok.Builder;
import lombok.Data;
import lombok.extern.jackson.Jacksonized;

@Data
@Builder
@Jacksonized
public class ProductSaPremiumOutput {

    /**
     * 险种Id
     */
    private Long productId;

    /**
     * 险种code
     */
    private String productCode;

    /**
     * 险种version
     */
    private String productVersion;

    /**
     * 被waiver的险种Id
     */
    private Long attachedToProductId;

    /**
     * 险种名称
     */
    private String productName;

    /**
     * product calculation method
     */
    private SaPremiumCalculationMethodEnum calculationMethod;

    /**
     * 期缴-标准保费（不含税）
     */
    private String periodStandardPremium;

    /**
     * 期缴-标准保费（含税）
     */
    private String periodStandardPremiumIncTax;

    /**
     * 期缴-加费（不含税）
     */
    private String periodExtraPremium;

    /**
     * 期缴-加费（含税）
     */
    private String periodExtraPremiumIncTax;

    /**
     * 期缴-险种折扣保费
     */
    private String periodDiscountPremium;

    /**
     * 期缴-险种折扣保费
     */
    private String periodDiscountPremiumIncTax;

    /**
     * 险种折扣保费明细
     */
    private List<ProductDiscountPremiumOutput> discountDetails;

    /**
     * 期缴-税合计
     */
    private String periodTotalTax;

    /**
     * 加费
     */
    private String periodLevy;

    /**
     * service 费用总计
     */
    private String totalServiceFee;

    /**
     * commission 费用总计
     */
    private String totalCommissionFee;

    /**
     * 期缴-营销活动总折扣
     */
    private String periodCampaignDiscount;

    /**
     * 期缴净保费
     * periodNetPremium = periodStandardPremium
     * + periodExtraPremium
     * - periodDiscountPremium
     * + totalServiceFee
     * + totalCommissionFee
     */
    private String periodNetPremium;

    /**
     * 期缴原始保费(不含Campaign)
     * periodUnDiscountedPremium = periodFinalPremium + periodCampaignDiscount
     */
    private String periodUnDiscountedPremium;

    /**
     * 期缴最终总费用
     * periodFinalPremium = periodStandardPremium
     * + periodExtraPremium
     * - periodDiscountPremium
     * + periodTotalTax
     * + periodLevy
     * + totalServiceFee
     * + totalCommissionFee
     * - periodCampaignDiscount
     */
    private String periodFinalPremium;

    // planned premium

    /**
     * 期缴-标准-不含税-PlannedPremium
     */
    private String periodStandardPlannedPremium;

    /**
     * 期缴-标准-含税-PlannedPremium
     */
    private String periodStandardPlannedPremiumIncTax;

    /**
     * 年化-标准-不含税-PlannedPremium
     */
    private String annualStandardPlannedPremium;

    /**
     * 年化-标准-含税-PlannedPremium
     */
    private String annualStandardPlannedPremiumIncTax;

    /**
     * 期缴-不含税-Regular Topup
     */
    private String periodRegularTopUp;

    /**
     * 期缴-含税-Regular Topup
     */
    private String periodRegularTopUpIncTax;

    /**
     * 年化-不含税-Regular Topup
     */
    private String annualRegularTopUp;

    /**
     * 年化-含税-Regular Topup
     */
    private String annualRegularTopUpIncTax;

    /**
     * 单次-不含税-Single Topup
     */
    private String periodSingleTopUp;

    /**
     * 单次-含税-Single Topup
     */
    private String periodSingleTopUpIncTax;

    /**
     * 险种基准保额
     */
    private String sumInsured;

    /**
     * 营销活动赠送保额
     */
    private String campaignFreeSumInsured;

    /**
     * 最终保额
     */
    private String finalSumInsured;

    /**
     * periodNoClaimDiscount
     */
    private String periodNoClaimDiscount;

    /**
     * periodNoClaimDiscountIncTax
     */
    private String periodNoClaimDiscountIncTax;

    /**
     * 分期 - 总保费
     */
    private String coverageTotalFinalPremium;

    /**
     * the standard premium of coverage total
     */
    private String coverageTotalStandardPremium;

    /**
     * the net premium of coverage total
     */
    private String coverageTotalNetPremium;

    /**
     * the total tax of coverage total
     */
    private String coverageTotalTax;

    /**
     * the premium discount of coverage total
     */
    private String coverageTotalPremiumDiscount;

    /**
     * the extra premium of coverage total
     */
    private String coverageTotalExtraPremium;

    /**
     * the campaign discount of coverage total
     */
    private String coverageTotalCampaignDiscount;

    /**
     * coverageTotalNoClaimDiscount
     */
    private String coverageTotalNoClaimDiscount;

    /**
     * 分期-短期费率
     */
    private String shortRate;

    /**
     * 责任费用明细
     */
    private List<LiabilitySaPremiumOutput> liabilities;

}
