/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import com.zatech.genesis.portal.lowcode.framework.auth.sdk.share.AuthenticationContextHolder;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: weizhen.kong
 */
@Configuration
@ConfigurationProperties(prefix = "sj")
@Setter
@Getter
public class UserConfig {

    public static final String UNIQAMNE = "uniqamne";

    private boolean d2cUserEnabled;

    private Map<String, UosNboUser> uosnboUser;

    /**
     *  D2C Quick Quote:Currently we are not calling B/M due to lack of registered agent account in NBO.
     *  "Solution: For now we agreed to use account from Stefan Jelusic to execute this call
     *  Next Step:
     *  D2C : hard code client Stefan's account as agent account and one more role"
     */
    private Map<String, UosNboUser> d2cUosnboUser;

    @Getter
    @Setter
    public static class UosNboUser {

        private String username;

        private String agentCode;

    }

    public String getUserName(boolean isD2c) {
        if (isD2c && d2cUserEnabled) {
            return Optional.ofNullable(d2cUosnboUser).map(map -> map.get(TraceOp.getTenant())).map(UosNboUser::getUsername).orElseGet(null);
        }
        return getUserName();
    }

    public String getAgentCode(boolean isD2c, OrderContext orderContext) {
        if (isD2c && d2cUserEnabled) {
            return Optional.ofNullable(d2cUosnboUser).map(map -> map.get(TraceOp.getTenant())).map(UosNboUser::getAgentCode).orElseGet(null);
        }
        return getAgentCode(orderContext);
    }

    public String getUserName() {
        return Optional.ofNullable(uosnboUser).map(map -> map.get(TraceOp.getTenant())).map(UosNboUser::getUsername).orElseGet(() -> AuthenticationContextHolder.getUsername().orElse(null));
    }

    public String getAgentCode(OrderContext orderContext) {
        return Optional.ofNullable(uosnboUser).map(map -> map.get(TraceOp.getTenant())).map(UosNboUser::getAgentCode).orElseGet(() -> {
            // NBO 要求谁出单,就用谁agent Code
            if (Objects.equals(TraceOp.getTenant(), UNIQAMNE)) {
                return orderContext.getExtension().getAgentCode();
            } else {
                // UOS要求使用登录人的agent code
                return AuthenticationContextHolder.getAgentCode().orElse(null);
            }
        });
    }

}