/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service;

import com.google.common.collect.Lists;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumCalcProductResponse;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumCalcResponse;
import com.zatech.genesis.market.api.split.response.LiabilitySaPremiumResponse;
import com.zatech.genesis.market.api.split.response.ProductSaPremiumResponse;
import com.zatech.genesis.policy.api.reqeust.IssuanceProductRequest;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.UnderwritingResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.BusinessHandleMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.MarketBusinessHandleDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.IExecutorRunner;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.result.BatchQuotationResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.enums.UwDecision;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.plugin.api.enums.TrailStepEnum;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.UnderWritingBusinessResult;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.UwOutput;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.UwUtils;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service.QuotationService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.octopus.component.sleuth.TraceOp;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.mapBy;
import static com.za.cqrs.util.Jackson.toJson;
import static com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum.UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE;
import static java.util.Optional.ofNullable;

@Slf4j
@Component
public class UnderwritingService {

    @Autowired
    private Converters converters;

    @Autowired
    IExecutorRunner executorRunner;

    @Autowired
    private QuotationService quotationService;

    @Autowired
    private IOuterPolicyService policyService;

    public Result onStop(UnderWritingBusinessResult result) {
        List<UnderWritingBusinessResult.UwInfo> uwResponseList = result.getResponse();
        if (CollectionUtils.isEmpty(uwResponseList)) {
            throw CommonException.byErrorAndParams(CommonBusinessHandleErrorCodes.POLICY_SYS_FAIL);
        }
        List<UwOutput.UwInfo> uwInfoList = uwResponseList.stream().map(uwResponse -> {
            UwDecision decision = UwUtils.map(uwResponse.getResponse());
            return UwOutput.UwInfo.builder()
                .pass(Boolean.TRUE.equals(uwResponse.getResponse().getPass()))
                .decision(decision)
                .orderNo(uwResponse.getOrderNo())
                .goodsId(uwResponse.getGoodsId())
                .details(mapBy(uwResponse.getResponse().getRuleResponses(), UwUtils::map))
                .build();
        }).collect(Collectors.toList());

        return UwOutput.builder().info(uwInfoList).build();
    }

    @Transactional
    public UnderWritingBusinessResult handle(IPhase phase, BasicUiModel uiModel, BusinessHandleContext context) {
        ContextHolder contextHolder = new ContextHolder(context.getOrder());
        List<Long> goodsIdList = uiModel.selectedGoodsId();
        List<IssuanceRequest> requestList = contextHolder.getOrderContextHolders()
            .stream()
            .map(OrderContextHolder::getPolicyRecord)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .filter(e -> goodsIdList.contains(e.getGoodsId()))
            .map(e -> {
                OrderContext orderContext = contextHolder.queryByOrderId(e.getOrderId()).getOrderContext();
                AutoGoods autoGoods = uiModel.selectedGoods(e.getGoodsId());
                AutoConvertIssuanceRequestContext issuanceRequestContext = new AutoConvertIssuanceRequestContext(new BusinessHandleContext(orderContext, TraceOp.getTenant()));
                issuanceRequestContext.setSelectedGoodsId(autoGoods.getGoodsId());
                issuanceRequestContext.setPhase(phase);
                IssuanceRequest request = converters.convert(uiModel, new IssuanceRequest(), issuanceRequestContext);
                request.setIssuanceNo(e.getIssuanceNo());
                request.setIssuanceTransactionType(UW_AND_CREATEISSUANCE_AND_CONFIRMISSUANCE);
                doIf(request.getTradeNo() == null, () -> request.setTradeNo(String.valueOf(System.currentTimeMillis())));
                doIf(request.getBizApplyNo() == null, () -> request.setBizApplyNo(String.valueOf(System.currentTimeMillis())));
                return request;
            }).collect(Collectors.toList());

        UnderWritingBusinessResult underWritingResult = new UnderWritingBusinessResult();
        if (CollectionUtils.isNotEmpty(requestList)) {
            BatchQuotationResult quotationResult = getQuotationResult(context);
            processQuotationResult(requestList, quotationResult, uiModel);
            List<UnderWritingBusinessResult.UwInfo> infoList = requestList
                .stream()
                .map(req -> {
                    log.info("PolicyFeign.underwriting request: {}", toJson(req));
                    UnderwritingResponse underwritingResponse = underwriting(req);
                    return UnderWritingBusinessResult.UwInfo.builder()
                        .response(underwritingResponse)
                        .orderNo(req.getBizApplyNo())
                        .goodsId(req.getGoodsId())
                        .build();
                }).collect(Collectors.toList());
            underWritingResult.setResponse(infoList);
        }
        return underWritingResult;
    }

    protected UnderwritingResponse underwriting(IssuanceRequest request) {
        return policyService.underwriting(request);
    }

    private void processQuotationResult(List<IssuanceRequest> requestList, BatchQuotationResult quotationResult, BasicUiModel uiModel) {
        Map<Long, SaPremiumCalcResponse> targetProductPremiumDict = quotationResult.getOriginResult().getCalcSaPremiumMultiPeriods()
            .stream()
            .filter(x -> {
                AutoGoods goods = uiModel.selectedGoods(x.getCoverageTotalCalcResponse().getGoodsId());
                return x.getCoverageTotalCalcResponse().getUnqueKey().equals(goods.getPlan().getPremiumCurrency().name());
            })
            .map(x -> x.getCoverageTotalCalcResponse())
            .collect(Collectors.toMap(SaPremiumCalcResponse::getGoodsId, Function.identity(), (e1, e2) -> e1));

        requestList
            .stream()
            .forEach(request -> request.setIssuanceProductList(
                ofNullable(request.getIssuanceProductList())
                    .orElse(Lists.newArrayList())
                    .stream()
                    .map(issuanceProductRequest -> {
                        foldQuotationResult(issuanceProductRequest, targetProductPremiumDict.get(request.getGoodsId()));
                        return issuanceProductRequest;
                    })
                    .collect(Collectors.toList())
            ));
    }

    private void foldQuotationResult(IssuanceProductRequest issuanceProduct, SaPremiumCalcResponse premiumCalcResponse) {
        if (premiumCalcResponse == null || getProductById(premiumCalcResponse, issuanceProduct.getProductId()) == null) {
            return;
        }
        SaPremiumCalcProductResponse saPremiumCalcProductResponse = getProductById(premiumCalcResponse, issuanceProduct.getProductId());
        val quotationProduct = ofNullable(saPremiumCalcProductResponse.getProductSaPremium()).orElse(new ProductSaPremiumResponse());

        if (StringUtils.isBlank(issuanceProduct.getPeriodStandardPremium())) {
            issuanceProduct.setPeriodStandardPremium(quotationProduct.getPeriodStandardPremium());
        }
        if (StringUtils.isBlank(issuanceProduct.getPeriodStandardPremiumIncTax())) {
            issuanceProduct.setPeriodStandardPremiumIncTax(quotationProduct.getPeriodStandardPremiumIncTax());
        }
        if (StringUtils.isBlank(issuanceProduct.getPeriodNetPremium())) {
            issuanceProduct.setPeriodNetPremium(decimal2Str(quotationProduct.getPeriodNetPremium()));
        }
        if (StringUtils.isBlank(issuanceProduct.getSumInsured())) {
            issuanceProduct.setSumInsured(quotationProduct.getSumInsured());
        }
        if (StringUtils.isBlank(issuanceProduct.getDiscountPremium())) {
            issuanceProduct.setDiscountPremium(quotationProduct.getPeriodDiscountPremium());
        }
        if (StringUtils.isBlank(issuanceProduct.getExtraPremium())) {
            issuanceProduct.setExtraPremium(quotationProduct.getPeriodExtraPremium());
        }

        //liability层级，一样的逻辑(仅插入productId下没有对应因子的，不进行update或insert)
        issuanceProduct.setIssuanceProductLiabilityList(
            ofNullable(issuanceProduct.getIssuanceProductLiabilityList())
                .orElse(Collections.emptyList())
                .stream()
                .map(issuanceLia -> {
                    ofNullable(saPremiumCalcProductResponse.getLiabilityList())
                        .orElse(Collections.emptyList())
                        .stream()
                        .filter(lia -> lia.getLiabilityId() != null && lia.getLiabilityId().equals(issuanceLia.getLiabilityId()))
                        .findFirst()
                        .ifPresent(quotationLia -> {
                            LiabilitySaPremiumResponse liabilitySaPremium = quotationLia.getLiabilitySaPremium();
                            if (liabilitySaPremium != null) {
                                if (StringUtils.isBlank(issuanceLia.getSumInsured())) {
                                    issuanceLia.setSumInsured(liabilitySaPremium.getSumInsured());
                                }
                                if (StringUtils.isBlank(issuanceLia.getPeriodStandardPremium())) {
                                    issuanceLia.setPeriodStandardPremium(liabilitySaPremium.getPeriodStandardPremium());
                                }
                                if (StringUtils.isBlank(issuanceLia.getPeriodStandardPremiumIncTax())) {
                                    issuanceLia.setPeriodStandardPremiumIncTax(liabilitySaPremium.getPeriodStandardPremiumIncTax());
                                }
                            }
                        });
                    return issuanceLia;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList())
        );
    }


    public SaPremiumCalcProductResponse getProductById(SaPremiumCalcResponse premiumCalcResponse, Long productId) {
        return premiumCalcResponse.getProductList().stream()
            .filter(x -> Objects.equals(x.getProductId(), productId))
            .findFirst().orElse(null);
    }

    private String decimal2Str(BigDecimal decimal) {
        return ofNullable(decimal)
            .map(BigDecimal::toPlainString)
            .orElse(null);
    }

    private BatchQuotationResult getQuotationResult(BusinessHandleContext context) {
        TrailStepEnum stepType = context.getTrailOpt()
            .map(trailContext -> TrailStepEnum.valueOf(trailContext.getStep()))
            .orElse(null);
        //preview页面核保的时候不在重新试算，直接查询quotation结果即可
        if (Objects.equals(TrailStepEnum.PREVIEW, stepType)) {
            BatchQuotationResult.OriginResult originResult = quotationService.queryQuotationResult(context.getOrder().getOrderNo());
            BatchQuotationResult batchQuotationResult = new BatchQuotationResult();
            batchQuotationResult.setOriginResult(originResult);
            return batchQuotationResult;
        }
        var request = new MarketBusinessHandleDslTemplate();
        request.getMetadata().setPhase(MarketPhase.quotation.name())
            .setProviders(List.of(new BusinessHandleMetaInfo.Provider().setName("quotation")));
        var results = executorRunner.runBusinessHandlers(context.getOrder().getOrderNo(), request);
        BatchQuotationResult batchQuotationResult = new BatchQuotationResult();
        results.headOption().ifPresent(result -> {
            if (result.isSuccess()) {
                BatchQuotationResult quotationResult = result.getOriginalResultIfSucceed();
                batchQuotationResult.setOriginResult(quotationResult.getOriginResult());
            } else {
                FailureResult failureResult = (FailureResult) result.getResult();
                log.error("Quotation error:{}", failureResult.getMessage());
                throw CommonException.byError(CommonBusinessHandleErrorCodes.POLICY_SYS_FAIL);
            }
        });
        return batchQuotationResult;
    }

}
