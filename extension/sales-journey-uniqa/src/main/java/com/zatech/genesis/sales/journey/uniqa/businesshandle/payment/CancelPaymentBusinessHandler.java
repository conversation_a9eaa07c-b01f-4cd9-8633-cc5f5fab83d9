/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.payment;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PaymentPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.common.processor.payment.errorcdoes.PaymentHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.processor.payment.param.CancelPayParams;
import com.zatech.genesis.sales.journey.payment.api.PaymentManager;
import com.zatech.genesis.sales.journey.payment.api.errorcodes.PaymentErrorCodes;
import com.zatech.genesis.sales.journey.payment.api.output.QueryPaymentInfoOutput;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@UIModelBusinessHandler(name = "cancelPayment", kind = BusinessHandlerKind.finance, desc = "Cancel payment request")
public class CancelPaymentBusinessHandler implements IAuthBusinessHandler<PaymentPhase, BasicUiModel, CancelPayParams>, IBusinessHandleTrailSupport<PaymentPhase, CancelPayParams, BasicUiModel> {

    @Autowired
    private PaymentManager paymentManager;

    @Override
    public PaymentPhase[] supportedPhases() {
        return new PaymentPhase[] {PaymentPhase.cancel};
    }

    @Override
    public Result onContinueError(Exception e, PaymentPhase paymentPhase, BasicUiModel basicUIModel, CancelPayParams cancelPaymentParams, BusinessHandleContext businessHandleContext) {
        throw CommonException.byErrorAndCause(PaymentHandleErrorCodes.CREATE_PAYMENT_ERROR, e);
    }

    @Override
    public Result onContinueSucceed(Result result, PaymentPhase paymentPhase, BasicUiModel basicUIModel, CancelPayParams cancelPaymentParams, BusinessHandleContext businessHandleContext) {
        return result;
    }

    @Override
    public Result onStop(PaymentPhase paymentPhase, BasicUiModel basicUIModel, CancelPayParams cancelPaymentParams, BusinessHandleContext businessHandleContext) {
        return null;
    }

    @Override
    public FlowStrategy handle(PaymentPhase paymentPhase, BasicUiModel basicUIModel, CancelPayParams cancelPaymentParams, BusinessHandleContext businessHandleContext) {
        QueryPaymentInfoOutput queryPaymentInfoOutput = paymentManager.findFirstByOrderIdOrderByGmtCreatedDesc(businessHandleContext.getOrder().getOrderNo()).orElseThrow(() -> CommonException.byError(PaymentErrorCodes.PYA_ORDER_NOT_FOUND));

        String thirdPartyOrderNo = queryPaymentInfoOutput.getThirdPartyOrderNo();
        cancelPaymentParams.setPayOrderNo(thirdPartyOrderNo);
        cancelPaymentParams.setExtensionParams((Map<String, Object>) Optional.ofNullable(businessHandleContext.getParams()).map(e -> e.get("extensionParams")).orElse(Collections.emptyMap()));
        return FlowStrategy.Continue;
    }

}
