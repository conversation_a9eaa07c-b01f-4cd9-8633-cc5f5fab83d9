/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.quotation;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.RenewPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.renew.param.RenewQuotationParam;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.uniqa.uimodel.serbia.SerbiaUiModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@Component
@UIModelBusinessHandler(name = "renewQuotation", kind = BusinessHandlerKind.renew, desc = "Serbia renew quotation business handler", tags = Consts.SERBIA)
public class SerbiaRenewQuotationHandlerHandler extends BaseRenewQuotationHandler implements IAuthBusinessHandler<RenewPhase, SerbiaUiModel, RenewQuotationParam> {

    @Override
    public RenewPhase[] supportedPhases() {
        return new RenewPhase[] {RenewPhase.quotation, RenewPhase.previewQuotation};
    }

    @Override
    public Result onContinueError(Exception e, RenewPhase phase, SerbiaUiModel businessModel, RenewQuotationParam param, BusinessHandleContext context) {
        return super.onContinueError(e, phase, param, businessModel, context);
    }

    @Override
    public Result onContinueSucceed(Result continueResult, RenewPhase phase, SerbiaUiModel businessModel, RenewQuotationParam param, BusinessHandleContext context) {
        return super.onContinueSucceed(continueResult, phase, param, businessModel, context);
    }

    @Override
    public FlowStrategy handle(RenewPhase phase, SerbiaUiModel businessModel, RenewQuotationParam param, BusinessHandleContext context) {
        super.handle(phase, param, businessModel, context);
        return FlowStrategy.Continue;
    }


}
