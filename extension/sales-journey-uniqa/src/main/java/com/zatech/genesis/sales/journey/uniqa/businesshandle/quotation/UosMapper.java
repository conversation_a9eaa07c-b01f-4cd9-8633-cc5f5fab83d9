/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation;

import com.zatech.genesis.gateway.api.uosnbo.request.CustomerRequest;
import com.zatech.genesis.gateway.api.uosnbo.request.VehicleInfoRequest;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.VehicleInfo;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Customer;
import com.zatech.octopus.framework.mapper.MapStructBaseMapper;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UosMapper extends MapStructBaseMapper {

    UosMapper INSTANCE = Mappers.getMapper(UosMapper.class);


    @Named("stringToBigDecimal")
    static BigDecimal stringToBigDecimal(String value) {
        if (value == null || StringUtils.isEmpty(value)) {
            return null;
        }
        return new BigDecimal(value);
    }

    @Mapping(source = "vehicleInvoiceValue", target = "vehicleInvoiceValue", qualifiedByName = "stringToBigDecimal")
    VehicleInfoRequest convert(VehicleInfo vehicleInfo);

    CustomerRequest convert(Customer customerRequest);

}