/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quickoffer.mapper;

import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.QuickQuotation;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.VehicleInfo;
import com.zatech.octopus.framework.mapper.MapStructBaseMapper;

import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: weizhen.kong
 */
@Mapper
public interface QuickOfferMapper extends MapStructBaseMapper {

    QuickOfferMapper INSTANCE = Mappers.getMapper(QuickOfferMapper.class);

    com.zatech.genesis.sales.journey.client.biz.auto.uimodel.VehicleInfo convert(VehicleInfo businessModel);

    com.zatech.genesis.sales.journey.client.biz.auto.uimodel.QuickQuotation convert(QuickQuotation businessModel);

}