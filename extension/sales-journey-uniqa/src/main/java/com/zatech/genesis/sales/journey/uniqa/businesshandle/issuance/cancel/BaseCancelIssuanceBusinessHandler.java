/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.cancel;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.cancel.param.CancelIssuanceParam;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.issuance.result.IssuanceBusinessResult;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service.CancelInsuranceService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class BaseCancelIssuanceBusinessHandler {

    @Autowired
    private CancelInsuranceService cancelInsuranceService;

    public Result onStop(BasicUiModel uiModel, CancelIssuanceParam param, BusinessHandleContext context) {
        return IssuanceBusinessResult.builder()
            .data(param.getCancelResponses()
                .stream()
                .map(IssuanceBusinessResult::map)
                .collect(Collectors.toList()))
            .build();
    }

    @Transactional
    public void handle(BasicUiModel uiModel, CancelIssuanceParam param, BusinessHandleContext context, String tag) {
        String reason = Optional.ofNullable(context.getParams())
            .map(e -> e.get("reason"))
            .map(String::valueOf)
            .orElse(null);

        cancelInsuranceService.cancel(context.getOrder().getOrderId(), reason);
    }

}
