/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.quotation;

import com.zatech.genesis.market.api.calculate.request.sapremium.SaPremiumCalcInitialPeriodRequest;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.RenewPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.renew.param.RenewQuotationParam;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.renew.result.RenewQuotationResult;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.model.Elements;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.result.QuotationResultConverter;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service.QuoteCheckService;
import com.zatech.genesis.sales.journey.uniqa.converter.GreenCardRequestConverter;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.AutoConvertIssuanceRequestContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.quotation.ConvertQuotationRequestContext;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BaseRenewQuotationHandler {

    @Autowired
    private QuotationResultConverter quotationResultConverter;

    @Autowired
    private Converters converters;

    @Autowired
    private GreenCardRequestConverter greenCardRequestConverter;

    @Autowired
    private QuoteCheckService quoteCheckService;

    public Result onContinueSucceed(Result result, RenewPhase phase, RenewQuotationParam param, BasicUiModel basicUiModel, BusinessHandleContext context) {
        if (result instanceof RenewQuotationResult renewQuotationResult) {
            return quotationResultConverter.convert(basicUiModel, renewQuotationResult.getOriginResult());
        } else {
            return result;
        }
    }

    public Result onContinueError(Exception e, RenewPhase phase, RenewQuotationParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        throw CommonException.byErrorAndCause(CommonBusinessHandleErrorCodes.RENEW_QUOTATION_SYS_FAIL, e);
    }

    public void handle(RenewPhase phase, RenewQuotationParam param, BasicUiModel businessModel, BusinessHandleContext context) {
        quoteCheckService.check(context.getOrder().getOrderNo(), businessModel);
        List<Long> selectedGoodsIdList;
        if (Objects.equals(RenewPhase.previewQuotation, phase)) {
            selectedGoodsIdList = businessModel.canSelectGoodsId();
        } else {
            selectedGoodsIdList = businessModel.selectedGoodsId();
        }
        List<IssuanceRequest> issuanceRequestList = selectedGoodsIdList.stream().map(goodsId -> {
            AutoConvertIssuanceRequestContext autoConvertIssuanceRequestContext = new AutoConvertIssuanceRequestContext();
            autoConvertIssuanceRequestContext.setOrderContext(context.getOrder());
            autoConvertIssuanceRequestContext.setBusinessHandleContext(context);
            autoConvertIssuanceRequestContext.setPhase(phase);
            autoConvertIssuanceRequestContext.setSelectedGoodsId(goodsId);
            autoConvertIssuanceRequestContext.setSelectedGoods(businessModel.selectedGoods(goodsId));
            IssuanceRequest issuanceRequest = new IssuanceRequest();
            issuanceRequest = converters.convert(businessModel, issuanceRequest, autoConvertIssuanceRequestContext);
            return issuanceRequest;
        }).toList();
        boolean issueWithGreenCard = Optional.ofNullable(businessModel.getTrafficCompulsoryInsuranceGoods())
            .filter(goods -> Objects.equals(true, goods.getSelected()))
            .map(AutoGoods::getElements)
            .map(Elements::getIssueWithGreenCard)
            .orElse(false);
        param.setGreenCard(issueWithGreenCard);
        if (issueWithGreenCard) {
            ConvertQuotationRequestContext convertQuotationRequestContext = new ConvertQuotationRequestContext(context);
            convertQuotationRequestContext.setSelectedGoodsId(businessModel.getTrafficCompulsoryInsuranceGoods().getGoodsId());
            convertQuotationRequestContext.setSelectedGoods(businessModel.getTrafficCompulsoryInsuranceGoods());
            convertQuotationRequestContext.setMarketPhase(MarketPhase.quotation);

            SaPremiumCalcInitialPeriodRequest saPremiumCalcInitialPeriodRequest = converters.convert(businessModel, new SaPremiumCalcInitialPeriodRequest(), convertQuotationRequestContext);
            param.setGreenCardRequests(greenCardRequestConverter.buildGreenCardRequestBySaPremium(saPremiumCalcInitialPeriodRequest));
        }
        param.setRenewQuotationRequest(handleMultiCurrency(issuanceRequestList));
    }

    private List<IssuanceRequest> handleMultiCurrency(List<IssuanceRequest> issuanceRequestList) {
        //mtpl and mtpl rider 币种不一样，但是journey里边需要汇总，这个时候policy不会计算mtpl的EUR的钱，需要手动把mtpl的baseCurrency改成EUR以获取
        boolean isSameCurrency = issuanceRequestList.stream()
            .map(issuanceRequest -> issuanceRequest.getMultiCurrency().getPremiumCurrency())
            .distinct()
            .count() == 1;
        if (issuanceRequestList.size() > 1 && !isSameCurrency) {
            IssuanceRequest baseIssuanceRequest = issuanceRequestList.stream().filter(issuanceRequest -> !Objects.equals(issuanceRequest.getMultiCurrency().getPremiumCurrency(), issuanceRequest.getMultiCurrency().getBaseCurrency())).findFirst().orElse(null);
            List<IssuanceRequest> list = issuanceRequestList.stream().filter(issuanceRequest -> Objects.equals(issuanceRequest.getMultiCurrency().getPremiumCurrency(), issuanceRequest.getMultiCurrency().getBaseCurrency())).toList();
            if (baseIssuanceRequest != null) {
                list.forEach(x -> x.getMultiCurrency().setBaseCurrency(baseIssuanceRequest.getMultiCurrency().getPremiumCurrency()));
            }
        }
        return issuanceRequestList;
    }

}
