/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.enums.LogLevel;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

/**
 * <AUTHOR>
 * @create 2024/4/15 09:43
 **/
public enum UniqaErrorCodes implements IErrorCode {
    //TODO 为了通过pipeline检查，enum不能为空，有了具体的异常之后可删除
    @StandardErrorCode(logLevel = LogLevel.warn)
    waiting_for_payment,

    @StandardErrorCode(logLevel = LogLevel.warn)
    issuing_policy,

    @StandardErrorCode(logLevel = LogLevel.warn)
    disability_discount_company,

    @StandardErrorCode(logLevel = LogLevel.warn)
    disability_discount_passport,

    @StandardErrorCode(logLevel = LogLevel.warn)
    disability_discount;

    @Override
    public String getModuleName() {
        return "uniqa";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
