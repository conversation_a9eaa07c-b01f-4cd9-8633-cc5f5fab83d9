/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.masterpolicy;

import com.zatech.genesis.policy.management.api.base.agreement.MasterAgreementExtendInfo;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageProductInfoResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageProductLiabilityInfoResponse;
import com.zatech.genesis.policy.management.api.response.agreement.MasterAgreementPlanCoverageResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Map;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.tuple.Pair;

/**
 * <AUTHOR>
 * @date 2024/7/29 20:16
 **/
@Getter
@Setter
public class MasterPolicyConvertContext {

    private BasicUiModel uiModel;

    private boolean isInitModel;

    private OrderContext orderContext;

    Map<Long, MasterAgreementPlanCoverageResponse> goodsMap;

    Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap;

    Map<Long, MasterAgreementPlanCoverageProductLiabilityInfoResponse> liabilityMap;

    private MasterAgreementExtendInfo masterAgreementExtendInfo;

    public MasterPolicyConvertContext(BasicUiModel uiModel, boolean isInitModel, Map<Long, MasterAgreementPlanCoverageResponse> goodsMap,
                                      Map<Pair<Long, Long>, MasterAgreementPlanCoverageProductInfoResponse> productMap,
                                      Map<Long, MasterAgreementPlanCoverageProductLiabilityInfoResponse> liabilityMap,
                                      MasterAgreementExtendInfo masterAgreementExtendInfo,
                                      OrderContext orderContext) {
        this.uiModel = uiModel;
        this.isInitModel = isInitModel;
        this.goodsMap = goodsMap;
        this.productMap = productMap;
        this.liabilityMap = liabilityMap;
        this.masterAgreementExtendInfo = masterAgreementExtendInfo;
        this.orderContext = orderContext;
    }

}
