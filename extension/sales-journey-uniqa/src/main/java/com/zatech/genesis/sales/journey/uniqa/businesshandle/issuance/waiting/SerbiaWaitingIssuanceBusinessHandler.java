/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.waiting;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.serbia.SerbiaUiModel;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@UIModelBusinessHandler(name = "issuance", kind = BusinessHandlerKind.policy, desc = "Serbia waiting issuance business handler", tags =
    Consts.SERBIA)
public class SerbiaWaitingIssuanceBusinessHandler implements IAuthBusinessHandler<PolicyPhase, SerbiaUiModel,
    IssuanceResult>, IBusinessHandleTrailSupport<PolicyPhase, IssuanceResult, BasicUiModel> {

    @Autowired
    private BaseWaitingIssuanceBusinessHandler baseHandler;

    @Override
    public PolicyPhase[] supportedPhases() {
        return new PolicyPhase[]{PolicyPhase.waitingInsurance};
    }

    @Override
    public Result onStop(PolicyPhase phase, SerbiaUiModel uiModel, IssuanceResult param, BusinessHandleContext context) {
        return baseHandler.onStop(param);
    }

    @Override
    public FlowStrategy handle(PolicyPhase phase, SerbiaUiModel uiModel, IssuanceResult param, BusinessHandleContext context) {
        baseHandler.handle(phase, uiModel, param, context);
        return FlowStrategy.Stop;
    }

}
