/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.coveragecheck;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.CoverageCheckPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Consts;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.coveragecheck.param.CoverageCheckParam;
import com.zatech.genesis.sales.journey.uniqa.uimodel.croatia.CroatiaUiModel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@UIModelBusinessHandler(name = "coverageCheckData", kind = BusinessHandlerKind.common, desc = "coverageCheck信息", tags = Consts.CROATIA)
public class CroatiaCoverageCheckBusinessHandler extends AbstractCoverageCheckBusinessHandler implements IAuthBusinessHandler<CoverageCheckPhase, CroatiaUiModel, CoverageCheckParam> {

    @Override
    public CoverageCheckPhase[] supportedPhases() {
        return new CoverageCheckPhase[]{CoverageCheckPhase.coverageOperate, CoverageCheckPhase.coverageSearch};
    }

    @Override
    public Result onStop(CoverageCheckPhase phase, CroatiaUiModel uiModel, CoverageCheckParam coverageCheckParam, BusinessHandleContext context) {
        return onStop(phase, coverageCheckParam, context);
    }

    @Override
    public FlowStrategy handle(CoverageCheckPhase phase, CroatiaUiModel uiModel, CoverageCheckParam coverageCheckParam, BusinessHandleContext context) {
        buildData(coverageCheckParam, context);
        return FlowStrategy.Stop;
    }

}
