/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.recall;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.CommonPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.trail.TrailExtraInfo;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.sales.journey.client.biz.common.processor.recall.param.RecallParam;
import com.zatech.genesis.sales.journey.recall.api.errorcodes.RecallErrorCodes;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Date;

import lombok.extern.slf4j.Slf4j;

import org.jetbrains.annotations.Nullable;

@Slf4j
@UIModelBusinessHandler(name = "createRecall", kind = BusinessHandlerKind.common, desc = "recall")
public class RecallBusinessHandler implements IAuthBusinessHandler<CommonPhase, BasicUiModel, RecallParam>,
    IBusinessHandleTrailSupport<CommonPhase, BasicUiModel, RecallParam> {

    @Override
    public CommonPhase[] supportedPhases() {
        return new CommonPhase[] {CommonPhase.recall};
    }

    @Override
    public Result onContinueError(Exception e, CommonPhase recallIPhase, BasicUiModel basicUIModel, RecallParam recallParam, BusinessHandleContext businessHandleContext) {
        throw CommonException.byErrorAndCause(RecallErrorCodes.CREATE_RECALL_FAIL, e);
    }

    @Override
    public Result onContinueSucceed(Result result, CommonPhase recallIPhase, BasicUiModel basicUIModel, RecallParam recallParam, BusinessHandleContext businessHandleContext) {
        return result;
    }

    @Override
    public FlowStrategy handle(CommonPhase recallIPhase, BasicUiModel basicUiModel, RecallParam recallParam, BusinessHandleContext businessHandleContext) {
        recallParam.setOrderId(businessHandleContext.getOrder().getOrderId());
        recallParam.setExtension(new JsonMap());
        recallParam.setGenerateOrderTime(new Date());
        return FlowStrategy.Continue;
    }

    @Override
    public TrailExtraInfo extraInfo(CommonPhase commonPhase, BasicUiModel basicUiModel, Result result, @Nullable Exception e, RecallParam recallParam, BusinessHandleContext businessHandleContext) {
        return null;
    }
}
