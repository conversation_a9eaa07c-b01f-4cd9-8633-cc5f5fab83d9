/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Executor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.CommonPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo.model.ShareInfo;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo.param.ShareInfoParam;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo.result.ShareInfoResult;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @date 2024/6/27 15:21
 **/
@Slf4j
@UIModelBusinessHandler(name = "shareInfo", kind = BusinessHandlerKind.common, desc = "分享信息", fillParamMode = Executor.FillParamMode.specParam)
public class ShareInfoBusinessHandler implements IAuthBusinessHandler<CommonPhase, ShareInfoParam, BasicUiModel> {

    @Autowired
    UIModelServiceFactory uiModelServiceFactory;

    @Override
    public CommonPhase[] supportedPhases() {
        return new CommonPhase[]{CommonPhase.shareInfo};
    }

    @Override
    public Result onContinueError(Exception e, CommonPhase phase, ShareInfoParam param, BasicUiModel basicUiModel, BusinessHandleContext context) {
        return FailureResult.error(e.getMessage());
    }

    @Override
    public Result onContinueSucceed(Result continueResult, CommonPhase phase, ShareInfoParam param, BasicUiModel basicUiModel, BusinessHandleContext context) {
        return continueResult;
    }

    @Override
    public Result onStop(CommonPhase phase, ShareInfoParam param, BasicUiModel basicUiModel, BusinessHandleContext context) {
        return ShareInfoResult.builder().operateResult(true).build();
    }

    @Override
    public FlowStrategy handle(CommonPhase phase, ShareInfoParam param, BasicUiModel basicUiModel, BusinessHandleContext context) {
        ShareInfo shareInfo = new ShareInfo();
        ShareInfo.ShareInfoModel model = new ShareInfo.ShareInfoModel();
        model.setClientId(param.getClientId());
        model.setSalt(param.getSalt());
        model.setTimestamp(param.getTimestamp());
        model.setSignature(param.getSignature());
        shareInfo.setModel(model);
        uiModelServiceFactory.getCreateService().createBaggage(context.getOrder().getOrderNo(), List.of(shareInfo));
        return FlowStrategy.Stop;
    }

}
