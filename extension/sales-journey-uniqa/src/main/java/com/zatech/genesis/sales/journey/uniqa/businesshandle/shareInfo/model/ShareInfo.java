/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo.model;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.baggage.BaggageField;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.baggage.IOrderBaggage;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.baggage.OrderBaggage;

import lombok.Getter;
import lombok.Setter;

import static com.zatech.genesis.portal.lowcode.framework.client.sdk.baggage.BaggageField.FieldKey.fields_json;
import static com.zatech.genesis.sales.journey.uniqa.businesshandle.shareInfo.model.ShareInfo.SHARE_INFO;

/**
 * <AUTHOR>
 * @date 2024/6/27 15:31
 **/
@Setter
@Getter
@OrderBaggage(scenario = SHARE_INFO)
public class ShareInfo implements IOrderBaggage {

    public static final String SHARE_INFO = "shareInfo";

    @BaggageField(fields_json)
    private ShareInfoModel model;

    @Getter
    @Setter
    public static class ShareInfoModel {

        private String clientId;

        private String salt;

        private String timestamp;

        private String signature;

    }


}
