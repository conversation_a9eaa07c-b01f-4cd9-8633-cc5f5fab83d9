/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.insurance;

import com.google.common.collect.Lists;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.issuance.IssuanceService;
import com.zatech.genesis.sales.journey.client.biz.common.processor.issuance.result.IssuanceResult;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.service.InsuranceService;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class BaseIssuanceBusinessHandler {

    @Autowired
    private InsuranceService insuranceService;

    @Autowired
    private IssuanceService issuanceService;

    @Transactional
    public Result onStop(BasicUiModel uiModel, IssuanceResult issuanceResult, BusinessHandleContext context) {
        return insuranceService.onStop(issuanceResult);
    }

    @Transactional
    public void handle(PolicyPhase phase, BasicUiModel uiModel, IssuanceResult issuanceResult, BusinessHandleContext context) {
        BeanUtils.copyProperties(insuranceService.handle(phase, uiModel, context), issuanceResult);
        userConfirm(uiModel, issuanceResult);
    }

    public void userConfirm(BasicUiModel uiModel, IssuanceResult issuanceResult) {
        //user comfirm
        if (uiModel.getScenario().isD2A()) {
            Optional.ofNullable(issuanceResult.getCreateResponses()).orElseGet(Lists::newArrayList)
                .forEach(iss -> issuanceService.userConfirm(iss.getIssuanceStatus(), uiModel.getIssueWithoutPayment(), iss.getIssuanceNo()));
        }
    }
}
