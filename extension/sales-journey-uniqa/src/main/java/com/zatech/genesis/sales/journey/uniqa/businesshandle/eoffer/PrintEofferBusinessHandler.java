/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.eoffer;

import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IBusinessHandleTrailSupport;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PolicyPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.Goods;
import com.zatech.genesis.sales.journey.client.biz.common.model.enums.UwDecision;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.param.PrintEofferCaseParam;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.param.PrintEofferParam;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.result.EofferResult;
import com.zatech.genesis.sales.journey.client.biz.common.recall.OtpDestinationHolderProxy;
import com.zatech.genesis.sales.journey.integration.notification.AsyncSendNotificationHandler;
import com.zatech.genesis.sales.journey.integration.notification.NotificationCommand;
import com.zatech.genesis.sales.journey.order.api.output.PolicyRecordInfoOutput;
import com.zatech.genesis.sales.journey.share.dto.notification.send.BusinessNoTuple;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import static com.zatech.gaia.resource.notification.NotifyBusinessTypeEnum.PROPOSAL;

@Slf4j
@UIModelBusinessHandler(name = "printE_offer", kind = BusinessHandlerKind.policy, desc = "Print E offer")
public class PrintEofferBusinessHandler implements IAuthBusinessHandler<PolicyPhase, BasicUiModel, PrintEofferParam>, IBusinessHandleTrailSupport<PolicyPhase, PrintEofferParam, BasicUiModel> {

    @Autowired
    private AsyncSendNotificationHandler<NotificationCommand> asyncSendNotificationHandler;

    @Autowired
    OtpDestinationHolderProxy otpDestinationHolderProxy;

    private static final Long MTPL_GOODS_ID = 1217295619735552L;

    @Override
    public PolicyPhase[] supportedPhases() {
        return new PolicyPhase[] {PolicyPhase.e_offer};
    }

    @Override
    public Result onContinueError(Exception e, PolicyPhase paymentPhase, BasicUiModel basicUIModel, PrintEofferParam printEofferParam, BusinessHandleContext businessHandleContext) {
        throw CommonException.byErrorAndCause(CommonBusinessHandleErrorCodes.POLICY_SYS_FAIL, e);
    }

    @Override
    public Result onContinueSucceed(Result result, PolicyPhase paymentPhase, BasicUiModel basicUIModel, PrintEofferParam printEofferParam, BusinessHandleContext businessHandleContext) {
        EofferResult eofferResult = (EofferResult) result;
        ContextHolder contextHolder = new ContextHolder(businessHandleContext);
        log.info("PrintEofferBusinessHandler.onContinueSucceed basicUIModel:{}", StaticJsonParser.toJsonString(basicUIModel));

        for (PrintEofferCaseParam printEofferCaseParam : printEofferParam.getPrintEofferCaseParams()) {
            OrderContextHolder orderContextHolder = contextHolder.queryByIssuanceNo(printEofferCaseParam.getIssuanceNo());
            orderContextHolder.getPolicyRecord().map(PolicyRecordInfoOutput::getGoodsId).ifPresent(printEofferCaseParam::setGoodsId);
            printEofferCaseParam.setUwAccept(uwAccept(contextHolder.queryByIssuanceNo(printEofferCaseParam.getIssuanceNo()), basicUIModel));
        }
        log.info("PrintEofferBusinessHandler.onContinueSucceed printEofferParam:{}", StaticJsonParser.toJsonString(printEofferParam));
        CustomerConfirmCommand customerConfirmCommand = new CustomerConfirmCommand(contextHolder, eofferResult.getFileDetailList(), printEofferParam, otpDestinationHolderProxy);
        contextHolder
                .getOrderContextHolders()
                .stream()
                .map(OrderContextHolder::getPolicyRecord)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .filter(policyRecord -> Objects.nonNull(policyRecord.getIssuanceNo()))
                .map(PolicyRecordInfoOutput::getIssuanceNo)
                .findFirst()
                .ifPresent(proposalNo ->
                        customerConfirmCommand.setTriggerBusinessNo(new BusinessNoTuple(PROPOSAL, proposalNo)));
        log.info("Start sending e-mail notification e-mail notification of e-offer download, orderNo:{}", businessHandleContext.getOrder().getOrderNo());
        asyncSendNotificationHandler.asyncSendNotification(customerConfirmCommand);
        return result;
    }

    private boolean uwAccept(OrderContextHolder contextHolder, BasicUiModel basicUIModel) {
        if (contextHolder == null) {
            return false;
        }
        //先去投保单上的状态判断是否是核保通过
        boolean issuanceUwAccept = contextHolder.getIssuance()
            .map(e -> e.getIssuanceStatus() == IssuanceStatusEnum.WAITING_FOR_ISSUANCE || e.getIssuanceStatus() == IssuanceStatusEnum.EFFECTIVE).orElse(false);
        if (issuanceUwAccept) {
            return true;
        }
        //再取uiModel上的核保字段。
        return Optional.of(contextHolder)
            .flatMap(OrderContextHolder::getPolicyRecord)
            .map(PolicyRecordInfoOutput::getGoodsId)
            .map(basicUIModel::selectedGoods)
            .map(Goods::getUwDecision)
            .map(uwDecision -> UwDecision.ACCEPT == uwDecision).orElse(false);
    }

    @Override
    public FlowStrategy handle(PolicyPhase policyPhase, BasicUiModel basicUiModel, PrintEofferParam printEofferParam, BusinessHandleContext businessHandleContext) {
        ContextHolder contextHolder = new ContextHolder(businessHandleContext);
        List<OrderContextHolder> orderContextHolders = contextHolder.getOrderContextHolders();
        List<PrintEofferCaseParam> printEofferCaseParams =
                orderContextHolders
            .stream()
            .filter(e -> e.getPolicyRecord().isPresent())
            .filter(e -> !e.getPolicyRecord().get().matchGoodsId(basicUiModel.queryGoodsId(GoodsCodeEnum.PA).orElse(null)))
            .map(e -> {
                PolicyRecordInfoOutput policyRecord = e.getPolicyRecord().orElse(null);
                PrintEofferCaseParam printEofferCaseParam = new PrintEofferCaseParam();
                printEofferCaseParam.setOrderNo(e.getOrderNo());
                printEofferCaseParam.setIssuanceNo(policyRecord.getIssuanceNo());
                printEofferCaseParam.setPolicyNo(policyRecord.getPolicyNo());

                if (e.getPolicyRecord().get().matchGoodsId(basicUiModel.queryGoodsId(GoodsCodeEnum.MTPL_Riders).orElse(null))) {
                    fillMTPLIssuanceNo(basicUiModel, contextHolder, printEofferCaseParam, businessHandleContext);
                }
                return printEofferCaseParam;

            }).toList();
        printEofferParam.setMainOrderNo(contextHolder.getTransactionOrderNo().orElse(null));
        printEofferParam.setPrintEofferCaseParams(printEofferCaseParams);
        businessHandleContext.getUserInfo().ifPresent(printEofferParam::setUserInfo);
        return FlowStrategy.Continue;
    }

    private static void fillMTPLIssuanceNo(BasicUiModel basicUiModel, ContextHolder contextHolder, PrintEofferCaseParam printEofferCaseParam, BusinessHandleContext businessHandleContext) {
        ContextHolder temp = contextHolder;
        if (businessHandleContext.getOrder().isSubOrder()) {
            OrderContext orderContext = businessHandleContext.getOrder().getTransactionOrderContextOpt().orElse(businessHandleContext.getOrder());
            temp = new ContextHolder(orderContext);
        }
        temp.getOrderContextHolders()
            .stream()
            .filter(orderContextHolder -> orderContextHolder.getPolicyRecord().isPresent())
            .filter(orderContextHolder -> orderContextHolder.getPolicyRecord().get().matchGoodsId(MTPL_GOODS_ID))
            .findFirst()
            .ifPresent(holder -> {
                printEofferCaseParam.addExtendedInfo("MTPIssuanceNo", holder.getIssuanceNo());
            });
    }

}
