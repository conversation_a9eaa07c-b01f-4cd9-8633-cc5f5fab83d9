/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.enums.LogLevel;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public enum QuotationErrorCode implements IErrorCode {

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    PROMOTION_CODE_NOT_VALID,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400, logLevel = LogLevel.warn)
    RATABLE_NOT_MATCH,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400, logLevel = LogLevel.warn)
    ACCIDENT_SA_RATABLE_NOT_MATCH;

    @Override
    public String getModuleName() {
        return "auto";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
