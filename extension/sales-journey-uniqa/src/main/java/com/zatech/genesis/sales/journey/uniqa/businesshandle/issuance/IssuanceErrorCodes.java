package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance;

import com.zatech.genesis.portal.toolbox.exception.StandardErrorCode;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public enum IssuanceErrorCodes implements IErrorCode {

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_WITHDRAWN_PA,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_WITHDRAWN_MTPL_RIDER,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_WITHDRAWN_MTPL,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_WITHDRAWN_MHULL,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_EFFECTIVE,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ISSUANCE_STATUS_IS_NO_VALID,

    @StandardErrorCode(status = StandardErrorCode.HttpStatusCode.ValidationError$400)
    ORDER_ALREADY_CONFIRMED;

    @Override
    public String getModuleName() {
        return "issuance";
    }

    @Override
    public String getErrorCode() {
        return name();
    }

}
