/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModelRegistry;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2024/1/2 17:11
 **/
@Component
public class UniqaUIModelRegistry implements IUIModelRegistry {

    @Override
    public List<String> registerUIModelPackages() {
        return List.of("com.zatech.genesis.sales.journey.uniqa.uimodel");
    }

    @Override
    public List<Class<? extends IPhase>> registerPhases() {
        return Collections.emptyList();
    }

}
