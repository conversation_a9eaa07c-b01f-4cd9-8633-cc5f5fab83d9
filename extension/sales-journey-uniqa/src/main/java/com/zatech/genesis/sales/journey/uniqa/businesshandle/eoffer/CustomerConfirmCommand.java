/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.eoffer;

import com.za.taylor.model.factor.RuleFactor;
import com.zatech.gaia.resource.components.enums.policy.PolicyChannelAgentRoleEnum;
import com.zatech.genesis.partner.api.dto.response.AgentResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.share.function.LazySupplier;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ExtensionContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.UserInfoHolder;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.param.PrintEofferCaseParam;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.param.PrintEofferParam;
import com.zatech.genesis.sales.journey.client.biz.common.processor.eoffer.result.EofferResult;
import com.zatech.genesis.sales.journey.client.biz.common.recall.OtpDestinationHolderProxy;
import com.zatech.genesis.sales.journey.integration.notification.NotificationCommand;
import com.zatech.genesis.sales.journey.share.dto.notification.send.AttachmentDTO;
 import com.zatech.genesis.sales.journey.share.dto.notification.send.BusinessNoTuple;
import com.zatech.genesis.sales.journey.share.dto.notification.send.RecipientDTO;
import com.zatech.genesis.sales.journey.share.enums.EofferTypeEnum;
import com.zatech.genesis.sales.journey.share.enums.VerificationCustomerTypeEnum;
import com.zatech.genesis.sales.journey.share.enums.ZmartTriggerPointEnum;
import com.zatech.genesis.sales.journey.share.util.FullNameUtil;
import com.zatech.genesis.sales.journey.client.biz.common.recall.OrderEmailHolder;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.octopus.common.util.AssertUtil;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import static com.za.cqrs.util.Functions.doIf;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_AGENT_EMAIL;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_AGENT_NAME;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_AGENT_PHONE;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_CHANNEL_ROLE;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_GOODS_ID;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_SALES_CHANNEL;
import static com.zatech.genesis.sales.journey.share.common.Constants.KEY_SEND_E_OFFER_PARAMS;

public class CustomerConfirmCommand implements NotificationCommand {

    private final ContextHolder contextHolder;

    private final PrintEofferParam printEofferParam;

    private final List<EofferResult.FileDetail> fileDetailList;

    private final BasicUiModel basicUiModel;

    private final LazySupplier<AgentResponse> managerResponseSupplier;

    private final LazySupplier<AgentResponse> agentResponseSupplier;

    private final String holderEmail;

    @Getter
    @Setter
    private BusinessNoTuple triggerBusinessNo;

    public CustomerConfirmCommand(ContextHolder contextHolder, List<EofferResult.FileDetail> fileDetailList, PrintEofferParam printEofferParam, OtpDestinationHolderProxy otpDestinationHolderProxy) {
        this.contextHolder = contextHolder;
        this.fileDetailList = fileDetailList;
        this.printEofferParam = printEofferParam;
        this.basicUiModel = (BasicUiModel) contextHolder.getOrderContext().getUiModel().getDataOpt().orElse(null);
        AssertUtil.notNull(basicUiModel);

        OrderContextHolder orderContextHolder = contextHolder.getOrderContextHolder();
        this.managerResponseSupplier = new LazySupplier<>(() -> Optional.of(contextHolder).flatMap(ContextHolder::getUserInfoHolder).flatMap(UserInfoHolder::getAgentDetail).orElse(null));
        this.agentResponseSupplier = new LazySupplier<>(() -> Optional.ofNullable(orderContextHolder)
            .flatMap(OrderContextHolder::getExtensionContextHolder).flatMap(ExtensionContextHolder::getAgentInfo).orElse(null));

        this.holderEmail = new OrderEmailHolder(contextHolder::getOrderContext, otpDestinationHolderProxy).getEmails(VerificationCustomerTypeEnum.HOLDER).stream().findFirst().orElse(null);
    }

    @Override
    public String getTriggerPoint() {
        return ZmartTriggerPointEnum.CUSTOMER_CONFIRMATION.name();
    }

    @Override
    public Map<String, Object> getSimpleFactors() {
        Map<String, Object> simpleFactors = new HashMap<>();
        AgentResponse agentResponse = agentResponseSupplier.get();
        simpleFactors.put(KEY_AGENT_NAME, FullNameUtil.splicingFullName(agentResponse.getFirstName(), agentResponse.getLastName()));
        simpleFactors.put(KEY_AGENT_PHONE, agentResponse.getPhoneNo());
        simpleFactors.put(KEY_AGENT_EMAIL, agentResponse.getEmail());
        simpleFactors.put(KEY_SEND_E_OFFER_PARAMS, JsonParser.toJsonString(printEofferParam));
        return simpleFactors;
    }

    @Override
    public List<AttachmentDTO> getAttachmentList() {
        return fileDetailList.stream()
            .filter(e -> e.getEofferTypeEnum() == EofferTypeEnum.EMAIL)
            .map(eofferDetail -> {
                AttachmentDTO attachmentDTO = new AttachmentDTO();
                attachmentDTO.setName(eofferDetail.getFileName());
                attachmentDTO.setUniCode(eofferDetail.getFileUniqueCode());
                attachmentDTO.setType(eofferDetail.getFileFormat());
                return attachmentDTO;
            }).toList();
    }

    @Override
    public void completeRuleFactorData(RuleFactor<Serializable> factor) {
        String salesChannel = contextHolder.getOrderContext().getChannelCode();
        Integer channelRole = basicUiModel.getChanelRole().getCode();
        List<Long> goodsIds = printEofferParam.getPrintEofferCaseParams().stream().filter(PrintEofferCaseParam::isUwAccept).map(PrintEofferCaseParam::getGoodsId).toList();
        doIf(KEY_SALES_CHANNEL.equals(factor.getName()), () -> factor.setValue(salesChannel));
        doIf(KEY_CHANNEL_ROLE.equals(factor.getName()), () -> factor.setValue(channelRole));
        doIf(KEY_GOODS_ID.equals(factor.getName()), () -> factor.setValue(goodsIds.toArray(new Long[]{})));
    }

    @Override
    public RecipientDTO getRecipientDTO() {
        var recipientDTO = new RecipientDTO();
        recipientDTO.setDirectRecipientDestinations(List.of(holderEmail));
        String agentEmail = Optional.ofNullable(agentResponseSupplier.get()).map(AgentResponse::getEmail).filter(StringUtils::isNotBlank).orElse("");
        String userEmail = Optional.ofNullable(managerResponseSupplier.get()).map(AgentResponse::getEmail).filter(StringUtils::isNotBlank).orElse("");
        List<String> ccDestinations = Stream.of(agentEmail, userEmail).filter(StringUtils::isNotBlank).distinct().toList();
        recipientDTO.setDirectCcRecipientDestinations(ccDestinations);
        return recipientDTO;
    }

    @Override
    public boolean preCheck() {
        return printEofferParam.getPrintEofferCaseParams().stream().anyMatch(PrintEofferCaseParam::isUwAccept)
            && basicUiModel.getChanelRole() == PolicyChannelAgentRoleEnum.SERVICE_AGENT
            && StringUtils.isNotBlank(holderEmail);
    }

}
