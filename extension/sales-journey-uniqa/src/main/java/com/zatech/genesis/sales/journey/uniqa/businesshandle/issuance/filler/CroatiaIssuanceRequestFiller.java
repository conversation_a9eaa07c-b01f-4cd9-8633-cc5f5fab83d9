/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.issuance.filler;

import com.zatech.gaia.resource.graphene.policy.PolicyRelationTypeEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceRelationRequest;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.enums.UwDecision;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.croatia.CroatiaUiModel;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/14 14:18
 **/
@Component
public class CroatiaIssuanceRequestFiller extends AbstractIssuanceRequestFiller {

    @Override
    protected void setRelation(List<IssuanceRequest> requestList, BasicUiModel uiModel, ContextHolder contextHolder, BusinessHandleContext context) {
        boolean hasBothManual = hasManualDecision(uiModel.getCommercialInsuranceGoods())
            && hasManualDecision(uiModel.getTrafficCompulsoryInsuranceGoods());

        if (requestList.size() > 1 && !hasBothManual) {
            String relationPolicyNo = contextHolder.getTransactionOrderNo().orElseThrow();
            setRelationDetails(requestList, relationPolicyNo);
        }
    }

    private boolean hasManualDecision(AutoGoods goods) {
        return Optional.ofNullable(goods)
            .map(g -> UwDecision.MANUAL.equals(g.getUwDecision()))
            .orElse(false);
    }

    private void setRelationDetails(List<IssuanceRequest> requestList, String relationNo) {
        IssuanceRelationRequest request = new IssuanceRelationRequest();
        request.setRelationNo(relationNo);
        request.setType(PolicyRelationTypeEnum.BUNDLED_POLICY);
        request.setNumberOfIssuance(requestList.size());

        List<IssuanceRelationRequest> issuanceRelationRequestList = List.of(request);
        requestList.forEach(req -> req.setIssuanceRelationList(issuanceRelationRequestList));
    }

    @Override
    public Class<? extends BasicUiModel> type() {
        return CroatiaUiModel.class;
    }

}
