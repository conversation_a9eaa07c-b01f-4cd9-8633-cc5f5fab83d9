/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.RenewPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service.ConvertRenewIssuanceService;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.result.ConvertRenewIssuanceResult;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ConvertRenewIssuanceHandler {

    @Autowired
    private ConvertRenewIssuanceService convertRenewIssuanceService;

    public Result onStop(RenewPhase phase, BasicUiModel param, ConvertRenewIssuanceResult businessModel, BusinessHandleContext context) {
        convertRenewIssuanceService.convertRenewIssuance(param, context);
        return new ConvertRenewIssuanceResult();
    }

    public FlowStrategy handle(RenewPhase phase, BasicUiModel param, ConvertRenewIssuanceResult businessModel, BusinessHandleContext context) {
        return FlowStrategy.Stop;
    }

}
