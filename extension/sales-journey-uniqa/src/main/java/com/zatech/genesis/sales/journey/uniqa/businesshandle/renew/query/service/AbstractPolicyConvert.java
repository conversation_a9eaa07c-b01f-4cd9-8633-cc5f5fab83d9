/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service;

import com.zatech.gaia.resource.biz.DiscountTypeEnum;
import com.zatech.gaia.resource.biz.PlateTypeEnum;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.policy.api.reqeust.CampaignRequest;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.IssuanceCampaignDetailResResponse;
import com.zatech.genesis.policy.api.response.IssuanceCampaignInfoResResponse;
import com.zatech.genesis.policy.api.response.IssuanceCampaignPayerResponse;
import com.zatech.genesis.policy.api.response.IssuanceCampaignResResponse;
import com.zatech.genesis.policy.api.response.IssuanceProductResponse;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPlan;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPolicyElements;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.VehicleInfo;
import com.zatech.genesis.sales.journey.integration.market.MarketAdapter;
import com.zatech.genesis.sales.journey.integration.market.response.GoodsBasicInfoResp;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.share.common.Converters;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.IssuanceResponseCustomersConverter;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.ProposalResponseBasicUiModelConverter;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.campaign.IssuanceCampaignGoodsConverter;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.context.ConvertPolicyResponseContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import jakarta.annotation.Resource;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.doIfPresent;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;

/**
 * @Author: weizhen.kong
 */
public abstract class AbstractPolicyConvert {

    @Resource
    private Converters converters;

    @Resource
    private IOuterPolicyService policyService;

    @Resource
    private IssuanceResponseCustomersConverter customersConverter;

    @Resource
    private ProposalResponseBasicUiModelConverter uiModelConvert;


    @Resource
    private IssuanceCampaignGoodsConverter campaignGoodsConverter;

    @Resource
    private MarketAdapter marketAdapter;

    protected boolean support(ScenarioTypeEnum scenario) {
        return false;
    }

    protected BasicUiModel convert(IssuanceRequest request, BasicUiModel model, ConvertPolicyResponseContext context, PolicyResponse policyResponse) {
        policyService.convert(List.of(request)).stream()
            .findFirst()
            .ifPresent(issuanceResponse -> {
                context.setTemporary(true);
                issuanceResponse.setTemporary(YesNoEnum.YES);
                customersConverter.convert(issuanceResponse, model, context);
                uiModelConvert.convertInsuredObject(issuanceResponse, model, context);
                ofNullable(model.selectedGoods(issuanceResponse.getGoodsId())).ifPresent(goods -> {
                        AutoGoods convertGoods = converters.convert(issuanceResponse, goods, context);
                        if (request.getCampaignCodeList() != null) {
                            IssuanceCampaignResResponse response = convertIssuanceCampaignResResponse(request.getCampaignCodeList());
                            var converted = campaignGoodsConverter.convert(response, convertGoods, context);
                            Assert.isTrue(converted == convertGoods, "Goods must not change");
                        }
                        // IssuanceResp.Extensions => convertGoods.getExtensions()
                        doIfPresent(convertGoods.getElements(), () -> {
                            Map<String, Object> extensions = convertGoods.getElements().getExtensions();
                            AutoPolicyElements autoPolicyElements = new AutoPolicyElements();
                            //Only for pa standalone, for mtpl renewal, it will be reset null
                            autoPolicyElements.setSerialNumber(convertGoods.getElements().getSerialNumber());
                            // for HRV
                            autoPolicyElements.setExposure(convertGoods.getElements().getExposure());
                            autoPolicyElements.setFrequency(convertGoods.getElements().getFrequency());
                            autoPolicyElements.setPreviousPremiumGrade(convertGoods.getElements().getPreviousPremiumGrade());
                            autoPolicyElements.setPreviousInsuranceCompanyCode(convertGoods.getElements().getPreviousInsuranceCompanyCode());
                            autoPolicyElements.setPreviousMTPLRegistrationNumber(convertGoods.getElements().getPreviousMTPLRegistrationNumber());
                            autoPolicyElements.setPreviousMTPLPolicySerialNumber(convertGoods.getElements().getPreviousMTPLPolicySerialNumber());
                            if (!CollectionUtils.isEmpty(extensions)) {
                                if (extensions.containsKey("E_Frequency")) {
                                    autoPolicyElements.setFrequency(String.valueOf(extensions.get("E_Frequency")));
                                }
                                if (extensions.containsKey("E_Exposure")) {
                                    autoPolicyElements.setExposure(String.valueOf(extensions.get("E_Exposure")));
                                }
                                if (extensions.containsKey("E_PreviousMTPLRegistrationNumber")) {
                                    autoPolicyElements.setPreviousMTPLRegistrationNumber(String.valueOf(extensions.get("E_PreviousMTPLRegistrationNumber")));
                                }
                                if (extensions.containsKey("E_PreviousInsuranceCompanyCode")) {
                                    autoPolicyElements.setPreviousInsuranceCompanyCode(String.valueOf(extensions.get("E_PreviousInsuranceCompanyCode")));
                                }
                                if (extensions.containsKey("E_PreviousPremiumGrade")) {
                                    autoPolicyElements.setPreviousPremiumGrade(String.valueOf(extensions.get("E_PreviousPremiumGrade")));
                                }
                            }
                            convertGoods.setElements(autoPolicyElements);
                        });
                        convertGoods.setExtensions(null);
                        var plan = Objects.requireNonNullElseGet(goods.getPlan(), AutoPlan::new);
                        if (goods.getPlan() != null) {
                            plan = converters.convert(issuanceResponse, plan, context);
                            convertGoods.setPlan(plan);
                        }
                    }
                );
                convertProducts(issuanceResponse, model, context);
                Optional.ofNullable(issuanceResponse.getAdditionalInfo()).ifPresent(issuanceAdditionalInfoResponse -> {
                    model.setPublicTender(issuanceAdditionalInfoResponse.getPublicTender() == YesNoEnum.YES);
                    model.setPublicTenderNo(issuanceAdditionalInfoResponse.getPublicTenderNo());
                });
            });
        //将Mhull得费用暴露出来
        model.getAllGoods().stream()
            .filter(Objects::nonNull)
            .filter(g -> !g.getGoodsCode().equals(GoodsCodeEnum.MHULL.getGoodsCode()))
            .forEach(x -> x.setCampaigns(Collections.emptyList()));
        model.getAllProducts().stream()
            .filter(p -> p.getProductId() != 911453179625479L)
            .filter(p -> Objects.nonNull(p.getLiabilities()))
            .forEach(p -> p.getLiabilities().getAllLiabilities().forEach(l -> {
                l.setPremiumDiscountList(Collections.emptyList());
                l.setSurchargeList(Collections.emptyList());
            }));
        return model;
    }

    private IssuanceCampaignResResponse convertIssuanceCampaignResResponse(List<CampaignRequest> campaignCodeList) {
        IssuanceCampaignResResponse response = new IssuanceCampaignResResponse();

        List<IssuanceCampaignInfoResResponse> issuanceCampaignInfoResResponses = campaignCodeList.stream()
            .map(campaignRequest -> {
                IssuanceCampaignInfoResResponse resResponse = new IssuanceCampaignInfoResResponse();
                resResponse.setCampaignCode(campaignRequest.getCampaignCode());

                List<IssuanceCampaignPayerResponse> issuanceCampaignPayerResponses = Optional.ofNullable(campaignRequest.getCampaignPayerList()).orElseGet(Collections::emptyList).stream()
                    .map(campaignPayerRequest -> {
                        IssuanceCampaignPayerResponse issuanceCampaignPayer = new IssuanceCampaignPayerResponse();
                        if (campaignPayerRequest.getDiscountType() == DiscountTypeEnum.AMOUNT) {
                            issuanceCampaignPayer.setAmount(campaignPayerRequest.getAmount());
                        } else {
                            issuanceCampaignPayer.setDiscountRate(campaignPayerRequest.getDiscountRate());
                        }
                        return issuanceCampaignPayer;
                    })
                    .collect(Collectors.toList());

                IssuanceCampaignDetailResResponse issuanceCampaignDetailResResponse = new IssuanceCampaignDetailResResponse();
                issuanceCampaignDetailResResponse.setIssuanceCampaignPayer(issuanceCampaignPayerResponses);

                resResponse.setIssuanceCampaignDetailList(Collections.singletonList(issuanceCampaignDetailResResponse));

                return resResponse;
            })
            .collect(Collectors.toList());

        response.setIssuanceCampaignInfoList(issuanceCampaignInfoResResponses);
        return response;
    }

    private void convertProducts(IssuanceResponse response, BasicUiModel model, ConvertPolicyResponseContext context) {
        var id2Product = of(response)
            .map(IssuanceResponse::getIssuanceProductList).orElseGet(Collections::emptyList).stream()
            .collect(Collectors.toMap(IssuanceProductResponse::getProductId, Function.identity()));
        doIfPresent(model.getProducts(), products ->
            products.getAllProducts()
                .forEach(product ->
                    doIfPresent(id2Product.get(product.getProductId()),
                        (existingProducts) -> converters.convert(existingProducts, product, context))));
        String plateType = ofNullable(model.getVehicleInfo()).map(VehicleInfo::getPlateType).orElse(null);
        if (Objects.equals(plateType, PlateTypeEnum.EXPORT.getCode().toString())) {
            doIfPresent(model.getProducts(), products -> products.remove(id2Product.keySet()));
        }
    }

    public Optional<GoodsBasicInfoResp> queryGoodsBasicOpt(Long goodsId) {
        return marketAdapter.queryGoodsBasicOpt(goodsId);
    }

    public boolean isMtplGoods(String goodsCode, Long goodsId) {
        return GoodsCodeEnum.MTPL.getGoodsCode().equals(Optional.ofNullable(goodsCode)
            .orElseGet(() -> queryGoodsBasicOpt(goodsId).map(GoodsBasicInfoResp::getGoodsCode).orElse(null)));
    }

}
