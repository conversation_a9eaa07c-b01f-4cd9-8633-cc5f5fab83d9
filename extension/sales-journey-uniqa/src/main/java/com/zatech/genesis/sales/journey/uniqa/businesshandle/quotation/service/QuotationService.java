/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.market.api.calculate.response.sapremium.CalcSaPremiumMultiPeriodResponse;
import com.zatech.genesis.market.api.calculate.response.sapremium.SaPremiumCalcResponse;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.BusinessHandleDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.BusinessHandleMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.MarketBusinessHandleDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.dsl.RenewBusinessHandleDslTemplate;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.RenewPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.ExecuteResults;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.executor.IExecutorRunner;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.portal.toolbox.util.ApplicationContextUtil;
import com.zatech.genesis.sales.journey.client.biz.auto.processor.quotation.result.BatchQuotationResult;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.holder.ContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.holder.OrderContextHolder;
import com.zatech.genesis.sales.journey.client.biz.common.model.premium.CalcSaPremiumMultiPeriod;
import com.zatech.genesis.sales.journey.order.api.QuotationRecordManager;
import com.zatech.genesis.sales.journey.order.api.output.QuotationRecordInfoOutput;
import com.zatech.genesis.sales.journey.uniqa.diff.DIffHandler;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class QuotationService {

    private final QuotationRecordManager quotationRecordManager;

    private final UIModelServiceFactory uiModelServiceFactory;

    public BatchQuotationResult.OriginResult queryQuotationResult(String orderNo) {
        UIModelOrderOutput<IUIModel> orderOutput = uiModelServiceFactory.getQueryService().query(orderNo);
        QuotationRecordInfoOutput quotationRecordInfoOutput = quotationRecordManager.query(orderOutput.getOrderId());
        BatchQuotationResult.OriginResult originResult = null;
        try {
            originResult = Optional.ofNullable(quotationRecordInfoOutput).map(x -> JsonParser.fromJsonString(x.getQuotationResult(), BatchQuotationResult.OriginResult.class)).orElse(null);

        } catch (Exception exception) {
            List<CalcSaPremiumMultiPeriod> calcSaPremiumMultiPeriodResponses = new ArrayList<>();
            Optional.of(quotationRecordInfoOutput).ifPresent(x -> calcSaPremiumMultiPeriodResponses.addAll(JsonParser.fromJsonStringToList(x.getQuotationResult(), CalcSaPremiumMultiPeriod.class)));
            originResult = new BatchQuotationResult.OriginResult();

            List<CalcSaPremiumMultiPeriodResponse> calcSaPremiumMultiPeriods = calcSaPremiumMultiPeriodResponses.stream().map(e -> StaticJsonParser.copyObject(e, CalcSaPremiumMultiPeriodResponse.class)).toList();
            originResult.setCalcSaPremiumMultiPeriods(calcSaPremiumMultiPeriods);
            if (CollectionUtils.size(calcSaPremiumMultiPeriodResponses) > 0) {
                CalcSaPremiumMultiPeriod calcSaPremiumMultiPeriod = calcSaPremiumMultiPeriodResponses.get(0);
                originResult.setExchangeDate(calcSaPremiumMultiPeriod.getExchangeDate());
                originResult.setGreenCardFee(calcSaPremiumMultiPeriod.getGreenCardFee());
                originResult.setExchangeRate(calcSaPremiumMultiPeriod.getExchangeRate());
            }
        }

        if (Objects.nonNull(originResult)) {
            return originResult;
        }

        OrderContext orderContext = orderOutput.getTransactionOrderContextOpt().orElse(orderOutput);
        String mainOrderNo = orderContext.getOrderNo();
        if (StringUtils.isBlank(mainOrderNo) || mainOrderNo.equals(orderNo)) {
            return originResult;
        }
        log.info("QuotationService.queryQuotationResult, quotationResult of orderNo({}) does not exist, continue to query mainOrderNo({})", orderNo, mainOrderNo);
        return queryQuotationResult(orderContext.getOrderNo());
    }

    public BatchQuotationResult.OriginResult queryQuotationResult(String orderNo, Long... goodsIds) {
        BatchQuotationResult.OriginResult originResult = queryQuotationResult(orderNo);
        if (originResult == null) {
            return null;
        }
        if (ArrayUtils.isEmpty(goodsIds)) {
            return originResult;
        }

        var goodsIdSet = Set.of(goodsIds);
        List<CalcSaPremiumMultiPeriodResponse> filteredList = originResult.getCalcSaPremiumMultiPeriods().stream().filter(e -> Optional.ofNullable(e.getCoverageTotalCalcResponse()).map(SaPremiumCalcResponse::getGoodsId).map(goodsIdSet::contains).orElse(false)).toList();
        originResult.setCalcSaPremiumMultiPeriods(filteredList);
        return originResult;
    }

    public BatchQuotationResult.OriginResult queryQuotationResultForHrv(ContextHolder contextHolder, Long... goodsIds) {
        OrderContextHolder orderContextHolder = contextHolder.getOrderContextHolder();
        String orderNo = orderContextHolder.getOrderContext().getOrderNo();
        if (!orderNo.startsWith("z_")) {
            return queryQuotationResult(orderNo, goodsIds);
        }

        try {
//迁移完成后，删除下面代码。用queryQuotationResult方法
            BatchQuotationResult.OriginResult originResult = queryQuotationResult(orderNo, goodsIds);
            if (originResult != null) {
                return originResult;
            }
            ExecuteResults results = ApplicationContextUtil.getBean(IExecutorRunner.class).runBusinessHandlers(orderNo, buildHandleTemplate(orderContextHolder));
            BatchQuotationResult batchQuotationResult = new BatchQuotationResult();
            results.headOption().ifPresent(result -> {
                if (result.isSuccess()) {
                    Optional.ofNullable(result.getOriginalResultIfSucceed()).map(e -> ((BatchQuotationResult) e).getOriginResult()).ifPresent(batchQuotationResult::setOriginResult);
                } else {
                    log.error("hrv Migration orderQuotation error:{}", ((FailureResult) result.getResult()).getMessage());
                    throw CommonException.byError(CommonBusinessHandleErrorCodes.POLICY_SYS_FAIL);
                }
            });
            return batchQuotationResult.getOriginResult();
        } catch (Exception e) {
            log.info("hrv Migration orderQuotation error message Use uwQuotation to migrate the downgraded solution");
            ObjectNode objectNode = ApplicationContextUtil.getBean(DIffHandler.class).buildUwOrderQuotationNode(orderNo, contextHolder);
            return StaticJsonParser.copyObject(objectNode, BatchQuotationResult.class).getOriginResult();
        }
    }

    public BusinessHandleDslTemplate buildHandleTemplate(OrderContextHolder orderContextHolder) {
        boolean isRenew = orderContextHolder.getWaitingForIssuance().map(IssuanceResponse::getIsRenewalPolicy).map(YesNoEnum.YES::equals).orElse(false);
        if (isRenew) {
            BusinessHandleDslTemplate request = new RenewBusinessHandleDslTemplate();
            request.getMetadata().setPhase(RenewPhase.quotation.name())
                .setProviders(List.of(new BusinessHandleMetaInfo.Provider().setName("renewQuotation")));
            return request;
        }

        BusinessHandleDslTemplate request = new MarketBusinessHandleDslTemplate();
        request.getMetadata().setPhase(MarketPhase.quotation.name())
            .setProviders(List.of(new BusinessHandleMetaInfo.Provider().setName("quotation")));
        return request;
    }

}
