/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.campaign;

import com.google.common.collect.Lists;
import com.za.taylor.core.TaylorProxy;
import com.za.taylor.enums.DecisionType;
import com.za.taylor.enums.RuleActionType;
import com.za.taylor.model.RuleResult;
import com.zatech.genesis.market.api.calculate.request.sapremium.CampaignRequest;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.MarketPhase;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoProduct;
import com.zatech.genesis.sales.journey.client.biz.common.model.rule.RuleCheckContext;
import com.zatech.genesis.sales.journey.client.biz.common.model.rule.RuleFactorModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.common.CommonConvertContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.rulecheck.UiModelToRuleConverterBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.loopIfPresent;
import static java.util.Optional.ofNullable;

@Slf4j
@Component
public class DiscountRuleFilter {

    @Autowired
    private TaylorProxy taylorProxy;

    @Autowired
    private UiModelToRuleConverterBuilder uiModelToRuleConverterBuilder;

    public void filter(BasicUiModel model, AutoGoods goods, CommonConvertContext context, List<CampaignRequest> campaignRequests, MarketPhase marketPhase, List<AutoProduct> productList) {
        Optional.ofNullable(productList).orElseGet(Lists::newArrayList).forEach(autoProduct -> {
            Optional.ofNullable(autoProduct.getLiabilities()).ifPresent(autoLiabilities ->
                loopIfPresent(autoLiabilities.getSelectedLiabilities(), liability -> {
                    Optional.ofNullable(liability.getPremiumDiscountList()).ifPresent(premiumDiscounts -> premiumDiscounts.removeIf(discount -> {
                        val removed = isDeclinedByRule(discount.getConditionRuleCode(), model, goods, context, campaignRequests, marketPhase);
                        doIf(removed, () -> log.info("Discount(liabilityId:{} premiumDiscountType:{}) removed by rule {}", liability.getLiabilityId(), discount.getPremiumDiscountType(), discount.getConditionRuleCode()));
                        return removed;
                    }));
                    Optional.ofNullable(liability.getSurchargeList()).ifPresent(surcharges -> surcharges.removeIf(extra -> {
                        val removed = isDeclinedByRule(extra.getConditionRuleCode(), model, goods, context, campaignRequests, marketPhase);
                        doIf(removed, () -> log.info("ExtraPremium(liabilityId:{} loadingType:{} loadingMethod:{}) removed by rule {}", liability.getLiabilityId(), extra.getSurchargeExtraLoadingType(), extra.getSurchargeLoadingMethod(), extra.getConditionRuleCode()));
                        return removed;
                    }));
                }));
        });
    }

    public boolean isDeclinedByRule(String ruleCode, BasicUiModel model, AutoGoods goods, CommonConvertContext context, List<CampaignRequest> campaignRequests, MarketPhase marketPhase) {
        if (!StringUtils.hasText(ruleCode)) {
            return false;
        }
        var sourceInput = taylorProxy.fetchInput(ruleCode);
        if (!sourceInput.available()) {
            log.warn("Rule (CODE: {}) is not available.", ruleCode);
            return false;
        }

        UiModelToRuleConverterBuilder.BasicUiModelToRuleConverter uiModelToRuleConverter = uiModelToRuleConverterBuilder.getOne().selectedGoodsId(goods.getGoodsId()).build();
        RuleCheckContext ruleCheckContext = RuleCheckContext.builder().orderContext(context.getOrderContext()).trailContextOpt(context.getTrailContextOpt()).marketPhase(marketPhase).selectedGoods(goods).selectedGoodsId(goods.getGoodsId()).build();
        RuleFactorModel ruleFactorModel = uiModelToRuleConverter.convert(model, new RuleFactorModel(), ruleCheckContext);
        campaignRequestToRuleFactorModel(ruleFactorModel, campaignRequests);
        val fireRuleInput = ruleFactorModel.buildRuleFactorValue().toTaylorInput(sourceInput);
        if (fireRuleInput == null) {
            log.warn("Rule (CODE: {}) is null.", ruleCode);
            return false;
        }
        val result = taylorProxy.fire(fireRuleInput);

        val decisionMessages = Optional.ofNullable(result)
            .map(RuleResult::getMessages)
            .orElseGet(Lists::newArrayList)
            .stream()
            .filter(x -> Objects.equals(x.getActionType(), RuleActionType.GENERAL_DECISION))
            .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(decisionMessages)) {
            return decisionMessages.stream()
                .anyMatch(x -> x.getDecisionType() != DecisionType.ACCEPT);
        }
        return false;
    }

    private void campaignRequestToRuleFactorModel(RuleFactorModel ruleFactorModel, List<CampaignRequest> campaignRequests) {
        if (CollectionUtils.isEmpty(campaignRequests)) {
            return;
        }

        List<RuleFactorModel.CampaignDiscount> campaignDiscountList = new ArrayList<>();
        campaignRequests.forEach(campaignRequest ->
            Optional.ofNullable(campaignRequest.getCampaignCode()).ifPresent(code -> {
                RuleFactorModel.CampaignDiscount campaignDiscount = new RuleFactorModel.CampaignDiscount();
                campaignDiscount.setCampaignCode(code);
                ofNullable(campaignRequest.getPayers()).flatMap(campaignPayerRequestList -> campaignPayerRequestList.stream().findFirst()).ifPresent(campaignPayerRequest -> {
                    campaignDiscount.setDiscountRate(campaignPayerRequest.getDiscountRate());
                    campaignDiscount.setDiscountAmount(campaignPayerRequest.getDiscountAmount());
                });
                campaignDiscountList.add(campaignDiscount);
            }));
        ruleFactorModel.setCampaignDiscountList(campaignDiscountList);
    }

}
