/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.intent;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandlerKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.IAuthBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.UIModelBusinessHandler;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.enums.FlowStrategy;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.CommonPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.CommonBusinessHandleErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.leadsform.param.LeadsForm;
import com.zatech.genesis.sales.journey.client.biz.common.businesshandler.leadsform.result.LeadsFormResult;
import com.zatech.genesis.sales.journey.integration.c360.request.LeadsFormRequest;
import com.zatech.genesis.sales.journey.service.common.util.RateLimiter;
import com.zatech.genesis.sales.journey.uniqa.errorcode.LeadsFormErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Map;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

@Slf4j
@UIModelBusinessHandler(name = "leadsForm", kind = BusinessHandlerKind.common, desc = "提交leads form")
public class BasedLeadsFormHandler implements IAuthBusinessHandler<CommonPhase, BasicUiModel, LeadsFormRequest> {

    @Autowired
    private TimelySend2C360Handler timelySend2C360Handler;

    @Value(value = "${sj.order.rate.limit: 5}")
    private int rateLimit;

    @Value(value = "${sj.order.rate.timeout: 60}")
    private int rateTimeout;

    @Autowired
    private RateLimiter rateLimiter;

    @Override
    public CommonPhase[] supportedPhases() {
        return new CommonPhase[] {CommonPhase.leads};
    }

    @Override
    public Result onContinueError(Exception e, CommonPhase phase, BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest,
                                  BusinessHandleContext context) {
        throw CommonException.byErrorAndCause(CommonBusinessHandleErrorCodes.INTENT_FORM_SYS_FAIL, e);
    }

    @Override
    public Result onContinueSucceed(Result continueResult, CommonPhase phase, BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest,
                                    BusinessHandleContext context) {
        return continueResult;
    }

    @Override
    public Result onStop(CommonPhase phase, BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest,
                         BusinessHandleContext context) {
        return new LeadsFormResult().setSuccess(true);
    }

    @Override
    public FlowStrategy handle(CommonPhase phase, BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest,
                               BusinessHandleContext context) {
        boolean result = rateLimiter.tryAcquireBaseOnCount(context.getOrder().getOrderNo(), rateLimit, rateTimeout);
        if (!result) {
            throw CommonException.byError(LeadsFormErrorCode.LEADS_FORM_SUBMIT_LIMIT);
        }
        handleLeads(basicUiModel, leadsFormRequest, context);
        return FlowStrategy.Stop;
    }

    private LeadsForm getLeadsForm(BasicUiModel basicUiModel, BusinessHandleContext context) {
        Map<String, Object> params = context.getParams();
        if (!ObjectUtils.isEmpty(params)) {
            return StaticJsonParser.copyObject(params.get("leadsForm"), LeadsForm.class);
        }
        return basicUiModel.getLeadsForm();
    }

    public void handleLeads(BasicUiModel basicUiModel, LeadsFormRequest leadsFormRequest,
                            BusinessHandleContext context) {
        LeadsForm leadsForm = getLeadsForm(basicUiModel, context);
        timelySend2C360Handler.handleLeads(leadsFormRequest, leadsForm, context);
    }

}
