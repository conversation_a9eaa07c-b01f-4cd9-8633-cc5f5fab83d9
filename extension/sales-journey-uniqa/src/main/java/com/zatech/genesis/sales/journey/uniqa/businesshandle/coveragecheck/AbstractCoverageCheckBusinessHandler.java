/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.coveragecheck;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.CoverageCheckPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.coveragecheck.param.CoverageCheckParam;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.coveragecheck.result.CoverageCheckResult;
import com.zatech.genesis.sales.journey.uniqa.datavalidate.coveragecheck.model.CoverageCheckMessage;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;

import static com.zatech.genesis.sales.journey.uniqa.datavalidate.coveragecheck.model.CoverageCheckMessage.COVERAGE_CHECK_MESSAGE;
import static java.util.Optional.ofNullable;

public class AbstractCoverageCheckBusinessHandler {

    @Autowired
    private UIModelServiceFactory uiModelServiceFactory;

    protected Result onStop(CoverageCheckPhase phase, CoverageCheckParam coverageCheckParam, BusinessHandleContext context) {
        return switch (phase) {
            case coverageSearch -> handleSearch(context);
            case coverageOperate -> handleOperate(coverageCheckParam, context);
        };
    }

    protected Result handleSearch(BusinessHandleContext context) {
        CoverageCheckResult checkResult = new CoverageCheckResult();
        UIModelOrderOutput<? extends BasicUiModel> uiModelOrderOutput = uiModelServiceFactory.getQueryService().query(context.getOrder().getOrderId());
        uiModelOrderOutput.getBaggageOpt(COVERAGE_CHECK_MESSAGE).ifPresent(x -> {
            if (x instanceof CoverageCheckMessage coverageCheckMessage) {
                ofNullable(coverageCheckMessage.getModel()).ifPresent(model -> {
                    checkResult.setMessage(model.getMessage());
                    checkResult.setShowMessage(model.isShowMessage());
                });
            }
        });
        return checkResult;
    }

    protected Result handleOperate(CoverageCheckParam coverageCheckParam, BusinessHandleContext context) {
        CoverageCheckResult checkResult = new CoverageCheckResult();
        UIModelOrderOutput<? extends BasicUiModel> uiModelOrderOutput = uiModelServiceFactory.getQueryService().query(context.getOrder().getOrderNo());
        uiModelOrderOutput.getBaggageOpt(COVERAGE_CHECK_MESSAGE).ifPresent(x -> {
            if (x instanceof CoverageCheckMessage coverageCheckMessage) {
                ofNullable(coverageCheckMessage.getModel()).ifPresent(model -> {
                    CoverageCheckMessage newCoverageCheckMessage = new CoverageCheckMessage();
                    newCoverageCheckMessage.setModel(CoverageCheckMessage.CoverageCheckModel.builder().showMessage(coverageCheckParam.isShowMessage()).message(model.getMessage()).build());
                    uiModelServiceFactory.getUpdateService().updateBaggage(context.getOrder().getOrderId(), newCoverageCheckMessage);

                    checkResult.setMessage(newCoverageCheckMessage.getModel().getMessage());
                    checkResult.setShowMessage(newCoverageCheckMessage.getModel().isShowMessage());
                });
            }
        });
        return checkResult;
    }

    protected void buildData(CoverageCheckParam coverageCheckParam, BusinessHandleContext context) {
        Optional.ofNullable(context.getParams()).ifPresent(x -> {
            ofNullable(x.get("showMessage")).ifPresent(y -> coverageCheckParam.setShowMessage((Boolean) y));
        });
    }
}
