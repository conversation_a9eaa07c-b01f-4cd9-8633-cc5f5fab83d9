/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.renew.query.service;

import com.google.common.collect.Sets;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceTransactionTypeEnum;
import com.zatech.gaia.resource.components.enums.policy.PolicyStatusEnum;
import com.zatech.genesis.policy.api.reqeust.IssuanceRequest;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.UIModelServiceFactory;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.UpdateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoGoods;
import com.zatech.genesis.sales.journey.client.biz.common.model.Goods;
import com.zatech.genesis.sales.journey.client.biz.common.model.plan.Plan;
import com.zatech.genesis.sales.journey.integration.cdc.outer.CdcAdapter;
import com.zatech.genesis.sales.journey.integration.market.MarketAdapter;
import com.zatech.genesis.sales.journey.integration.policy.IOuterPolicyService;
import com.zatech.genesis.sales.journey.integration.renew.IOuterRenewService;
import com.zatech.genesis.sales.journey.integration.renew.request.RenewalCreateIssuanceRequest;
import com.zatech.genesis.sales.journey.plugin.api.response.InitUiModelResult;
import com.zatech.genesis.sales.journey.plugin.api.service.InitUiModelExtensionPoint;
import com.zatech.genesis.sales.journey.share.util.DateTimeUtils;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.FillCustomerRelation;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.issuance.reverse.context.ConvertPolicyResponseContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.enums.GoodsCodeEnum;
import com.zatech.genesis.sales.journey.uniqa.uimodel.montenegro.MontenegroUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.serbia.SerbiaUiModel;
import com.zatech.genesis.sales.journey.uniqa.units.PolicyDateManager;
import com.zatech.octopus.common.util.AssertUtil;
import com.zatech.octopus.common.util.DateUtil;
import com.zatech.octopus.component.sleuth.TraceOp;
import com.zhongan.graphene.cdc.scenario.share.dto.response.policy.PolicyResponseDTO;

import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Stream;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;

import static com.za.cqrs.util.Functions.doIfPresent;
import static java.util.Optional.ofNullable;

@Slf4j
@Service
@AllArgsConstructor
public class ConvertRenewIssuanceService {

    private final IOuterRenewService renewService;

    private final UIModelServiceFactory serviceFactory;

    private final IOuterPolicyService policyService;

    private final PreviousPolicyConvertService issuanceRequestBasicUiModelConverter;

    private final MarketAdapter marketAdapter;

    private final CdcAdapter cdcAdapter;

    private InitUiModelExtensionPoint initUiModelExtensionPoint;

    private static final String POLICY_NO = "policyNo";

    private static final String HRV_TENANT = "uniqahrv";

    public Set<ScenarioTypeEnum> mtplEffectiveDateResetMarkScenario() {
        return Sets.newHashSet(ScenarioTypeEnum.temporary, ScenarioTypeEnum.salesJourney, ScenarioTypeEnum.salesJourneyAgent, ScenarioTypeEnum.renew);
    }

    public void convertRenewIssuance(BasicUiModel param, BusinessHandleContext context) {
        BasicUiModel convertUiModel = doConvert(param, context.getOrder(), null, false);
        calculateResetDate(convertUiModel);
        doUpdate(convertUiModel, context.getOrder().getOrderNo());
    }

    public void calculateResetDate(BasicUiModel convertUiModel) {
        if (!isEligibleForDateReset(convertUiModel)) {
            return;
        }

        AutoGoods goods = convertUiModel.getTrafficCompulsoryInsuranceGoods();
        Plan plan = goods.getPlan();
        Date effectiveDate = ofNullable(plan).map(Plan::getEffectiveDate).orElse(null);
        Date expireDate = ofNullable(plan).map(Plan::getExpireDate).orElse(null);
        plan.setLastPolicyExpiryDate(effectiveDate);

        if (convertUiModel instanceof MontenegroUiModel) {
            effectiveDate = handleMontenegroDateReset(effectiveDate, expireDate);
            expireDate = calculateExpireDate(convertUiModel, effectiveDate);
        } else if (convertUiModel instanceof SerbiaUiModel) {
            effectiveDate = handleSerbiaDateReset(effectiveDate);
            expireDate = calculateExpireDate(convertUiModel, effectiveDate);
        }

        plan.setEffectiveDate(effectiveDate);
        plan.setExpireDate(expireDate);
    }

    private Date calculateExpireDate(BasicUiModel model, Date effectiveDate) {
        if (shouldUseCoveragePeriod(model)) {
            return DateUtil.addDays(effectiveDate,
                Integer.parseInt(model.getTrafficCompulsoryInsuranceGoods().getPlan().getCoveragePeriod()));
        }
        return DateUtil.addYears(effectiveDate, 1);
    }

    private boolean shouldUseCoveragePeriod(BasicUiModel model) {
        if (model instanceof MontenegroUiModel) {
            return Objects.equals(ScenarioTypeEnum.export, model.getScenario());
        }
        if (model instanceof SerbiaUiModel) {
            return Objects.equals(ScenarioTypeEnum.temporary, model.getScenario())
                || Objects.equals(ScenarioTypeEnum.export, model.getScenario());
        }
        return false;
    }

    private boolean isEligibleForDateReset(BasicUiModel model) {
        return mtplEffectiveDateResetMarkScenario().contains(model.getScenario())
            && Boolean.TRUE.equals(Optional.ofNullable(model.getTrafficCompulsoryInsuranceGoods())
            .map(Goods::getSelected)
            .orElse(false));
    }

    private Date handleMontenegroDateReset(Date effectiveDate, Date expireDate) {
        return PolicyDateManager.calculateEffectiveDate(effectiveDate, expireDate, false);
    }

    private Date handleSerbiaDateReset(Date effectiveDate) {
        if (Objects.nonNull(effectiveDate) && effectiveDate.before(new Date())) {
            return DateTimeUtils.to24HourOfToDay(new Date(), ZoneId.systemDefault());
        }
        return effectiveDate;
    }

    public BasicUiModel convertOldPolicyWithoutUpdate(BasicUiModel param, DataProvideContext context) {
        BasicUiModel basicUiModel = doConvert(param, context.getOrder(), (String) ofNullable(context.getParams()).map(params -> params.get(POLICY_NO)).orElse(null), true);
        //renew的场景默认会把issueWithoutPayment重置为false，为了保证renew的时候用户可以修改该字段，需要把老保单的重置掉
        basicUiModel.setIssueWithoutPayment(null);
        return basicUiModel;
    }

    private BasicUiModel doConvert(BasicUiModel param, OrderContext orderContext, String policyNo, boolean returnNewUiModel) {
        boolean renew = ScenarioTypeEnum.renew.equals(param.getScenario());
        String oldPolicyNo = renew ? param.getOldPolicyNo() : policyNo;
        //PA的时候可能输入的是老核心的保单也可能是graphene的保单，老核心是直接用serialNO查询保单的，graphene需要用cdc查询出来serialNo对应的保单号
        if (!HRV_TENANT.equals(TraceOp.getTenant()) &&
                (param.getScenario().equals(ScenarioTypeEnum.passengerAccident) ||
                        param.getScenario().equals(ScenarioTypeEnum.renew))) {
            Page<PolicyResponseDTO> policyResponseDTOS = cdcAdapter.searchPolicyNewListByMtplSerialNo(oldPolicyNo);
            if (Objects.nonNull(policyResponseDTOS) && CollectionUtils.isNotEmpty(policyResponseDTOS.getContent())) {
                List<PolicyResponseDTO> policies = policyResponseDTOS.getContent();
                Stream<PolicyResponseDTO> policyStream = policies.stream()
                        .filter(e -> GoodsCodeEnum.MTPL.getGoodsCode().equals(e.getGoodsCode()));

                if (param.getScenario().equals(ScenarioTypeEnum.passengerAccident)) {
                    policyStream = policyStream.filter(e -> e.getPolicyStatus() == PolicyStatusEnum.POLICY_EFFECT);
                }

                Optional<PolicyResponseDTO> queryPolicyNoOpt = policyStream.findFirst();
                if (queryPolicyNoOpt.isPresent()) {
                    oldPolicyNo = queryPolicyNoOpt.get().getPolicyNo();
                }
            }
        }
        PolicyResponse policyResponse = policyService.queryPolicy(oldPolicyNo);
        if (policyResponse == null) {
            //置空，避免初始化数据影响前端置空判断
            param.setVehicleInfo(null);
            return param;
        }

        if (param.getScenario().equals(ScenarioTypeEnum.passengerAccident) && !GoodsCodeEnum.MTPL.getGoodsCode().equals(policyResponse.getGoodsCode())) {
            //置空，避免初始化数据影响前端置空判断
            param.setVehicleInfo(null);
            return param;
        }

        RenewalCreateIssuanceRequest request = new RenewalCreateIssuanceRequest();
        request.setEnableTaylor(false);
        request.setIssuanceTransactionType(IssuanceTransactionTypeEnum.WAITING_FOR_CREATEISSUANCE);
        request.setOriginalPolicyNo(oldPolicyNo);
        request.setRenewal(renew);
        IssuanceRequest issuanceRequest = renewService.generateRenewal(request);
        ConvertPolicyResponseContext convertPolicyResponseContext = new ConvertPolicyResponseContext(orderContext, issuanceRequest.getGoodsCode());

        if (returnNewUiModel) {
            ScenarioTypeEnum scenario = param.getScenario();
            String paramOldPolicyNo = param.getOldPolicyNo();
            InitUiModelResult initUiModelResult = initUiModelExtensionPoint.initUiModel(scenario.name());
            param = (BasicUiModel) initUiModelResult.getUiModel();
            param.setScenario(scenario);
            param.setOldPolicyNo(paramOldPolicyNo);
        }
        BasicUiModel basicUiModel = issuanceRequestBasicUiModelConverter.convert(param, convertPolicyResponseContext, policyResponse, issuanceRequest);
        //renew场景需要重置掉issueWithOutPayment，在application页面用户可以自己手动打开，但是会触发校验
        basicUiModel.setIssueWithoutPayment(false);
        calculateResetDate(basicUiModel);

        new FillCustomerRelation(basicUiModel).fillAllCustomerRelation();
        // C端不是1年险 去除这个字段 否则diff 会有影响
        specialShortRateCalcMethod(policyResponse, basicUiModel);

        return basicUiModel;
    }

    @Transactional
    public void doUpdate(BasicUiModel basicUiModel, String orderNo) {
        UIModelOrderOutput<BasicUiModel> basicUiModelUIModelOrderOutput = serviceFactory.getQueryService().query(orderNo);
        basicUiModelUIModelOrderOutput.getDataOpt().ifPresent(e -> {
            e.selectedGoodsId().forEach(g -> e.selectedGoods(g).getPlan().setLastPolicyNo(e.getOldPolicyNo()));
            basicUiModel.setOldPolicyNo(e.getOldPolicyNo());
            UpdateUIModelInput<BasicUiModel> updateUIModelInput = new UpdateUIModelInput<>(basicUiModel, null);
            serviceFactory.getUpdateService().updateOrThrow(orderNo, null, updateUIModelInput);
        });
    }

    private void specialShortRateCalcMethod(PolicyResponse policyResponse, BasicUiModel model) {
        AutoGoods autoGoods = model.getGoodsById(policyResponse.getGoodsId()).orElse(null);
        if (autoGoods == null){
            return;
        }
        Date effectiveDate = Optional.ofNullable(autoGoods.getPlan()).map(Plan::getEffectiveDate).orElse(null);
        Date expireDate = Optional.ofNullable(autoGoods.getPlan()).map(Plan::getExpireDate).orElse(null);
        if (effectiveDate != null && expireDate != null) {
            Date targetDate = DateUtils.addYears(effectiveDate, 1);
            if (targetDate.getTime() <= expireDate.getTime()) {
                doIfPresent(model.getProducts(), products -> {
                    for (var product : products.getAllProducts()) {
                        product.setShortRateCalcMethod(null);
                    }
                });
            }
        }
    }

}
