/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.service;

import com.zatech.genesis.gateway.api.uosnbo.request.CurrentPolicyRequest;
import com.zatech.genesis.gateway.api.uosnbo.request.CustomerRequest;
import com.zatech.genesis.gateway.api.uosnbo.request.PreviousPolicyRequest;
import com.zatech.genesis.gateway.api.uosnbo.request.QueryAndCheckRequest;
import com.zatech.genesis.gateway.api.uosnbo.result.UosBmResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.trail.TrailContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.exception.errorcode.SystemErrorCodes;
import com.zatech.genesis.sales.journey.client.biz.auto.enums.ScenarioTypeEnum;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPlan;
import com.zatech.genesis.sales.journey.client.biz.auto.uimodel.AutoPolicyElements;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Address;
import com.zatech.genesis.sales.journey.client.biz.common.model.customer.Customer;
import com.zatech.genesis.sales.journey.integration.gateway.IOutMtlGatewayService;
import com.zatech.genesis.sales.journey.integration.gateway.IntGatewayAdapter;
import com.zatech.genesis.sales.journey.plugin.api.enums.TrailStepEnum;
import com.zatech.genesis.sales.journey.uniqa.businesshandle.quotation.UosMapper;
import com.zatech.genesis.sales.journey.uniqa.errorcode.UosNboErrorCode;
import com.zatech.genesis.sales.journey.uniqa.uimodel.BasicUiModel;
import com.zatech.genesis.sales.journey.uniqa.uimodel.converter.common.CommonConvertContext;
import com.zatech.genesis.sales.journey.uniqa.uimodel.montenegro.MontenegroUiModel;
import com.zatech.genesis.sales.journey.uniqa.units.PolicyDateManager;
import com.zatech.octopus.common.util.DateUtil;
import com.zatech.octopus.core.util.JacksonUtil;

import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.za.cqrs.util.Functions.doIf;
import static com.za.cqrs.util.Functions.doIfPresent;

@Slf4j
@Component
public class UosNboService {

    @Autowired
    private IOutMtlGatewayService gatewayService;

    @Autowired
    private IntGatewayAdapter gatewayAdapter;

    @Autowired
    private UserConfig userConfig;

    public UosBmResponse getBonusMalus(BasicUiModel basicUiModel, CommonConvertContext context) {
        QueryAndCheckRequest bmRequest = convert(basicUiModel, context.getOrderContext(), context.getTrailContextOpt());
        UosBmResponse bm = gatewayAdapter.queryBM(bmRequest);
        if (!Boolean.TRUE.equals(bm.isSuccess())) {
            handUosNboException(bm.getMessage());
        }
        return bm;
    }

    public QueryAndCheckRequest convert(BasicUiModel basicUiModel, OrderContext orderContext, Optional<TrailContext> context) {
        QueryAndCheckRequest request = new QueryAndCheckRequest();
        request.setVehicleInfo(UosMapper.INSTANCE.convert(basicUiModel.getVehicleInfo()));
        request.setUserName(userConfig.getUserName(basicUiModel.getScenario().isD2C()));
        request.setAgentCode(userConfig.getAgentCode(basicUiModel.getScenario().isD2C(), orderContext));
        doIfPresent(basicUiModel.getVehicleInfo(), () -> {
            request.getVehicleInfo().setPlateType(basicUiModel.getVehicleInfo().getPlateType());
            doIf(Boolean.TRUE.equals(basicUiModel.getVehicleInfo().getVehicleLeasing()),
                () -> {
                    log.info("Vehicle user info:{}", JsonParser.toJsonString(basicUiModel.queryVehicleUser()));
                    request.setVehicleUser(UosMapper.INSTANCE.convert(basicUiModel.queryVehicleUser()));
                });
        });
        request.setInsured(UosMapper.INSTANCE.convert(basicUiModel.getInsured()));
        request.setHolder(UosMapper.INSTANCE.convert(basicUiModel.getPolicyHolder()));
        populateAddress(request.getInsured(), basicUiModel.getInsured());

        AutoPlan plan = basicUiModel.getTrafficCompulsoryInsuranceGoods().getPlan();
        Date expireDate = plan.getExpireDate();
        Date effectiveDate = plan.getEffectiveDate();

        //mne renew, basic info阶段没有录入生效时间 需要特殊处理
        if (isMneRenewStep(basicUiModel, context)) {
            effectiveDate = PolicyDateManager.calculateEffectiveDate(effectiveDate, expireDate);
            expireDate = DateUtil.addMonths(effectiveDate, 12);
        } else if (Objects.isNull(expireDate) && Objects.nonNull(effectiveDate)) {
            expireDate = DateUtil.addMonths(effectiveDate, 12);
            log.info("quick quotation step reset effectiveDate:{}, expireDate:{}", effectiveDate, expireDate);
        }

        CurrentPolicyRequest currentPolicyRequest = new CurrentPolicyRequest();
        currentPolicyRequest.setStartDate(effectiveDate);
        currentPolicyRequest.setEndDate(expireDate);
        Optional.ofNullable(effectiveDate).ifPresent(startTime ->
            currentPolicyRequest.setStartTime(startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalTime()));
        Optional.ofNullable(expireDate).ifPresent(endTime ->
            currentPolicyRequest.setEndTime(endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalTime()));
        request.setCurrentPolicy(currentPolicyRequest);

        AutoPolicyElements elements = basicUiModel.getTrafficCompulsoryInsuranceGoods().getElements();
        if (Objects.nonNull(elements.getPreviousInsuranceCompanyCode()) || Objects.nonNull(elements.getPreviousPremiumGrade()) || Objects.nonNull(elements.getPreviousMTPLRegistrationNumber())) {
            PreviousPolicyRequest previousPolicyRequest = new PreviousPolicyRequest();
            previousPolicyRequest.setPolicyNo(elements.getPreviousMTPLRegistrationNumber());
            previousPolicyRequest.setInsuranceId(elements.getPreviousInsuranceCompanyCode());
            previousPolicyRequest.setPremiumGrade(elements.getPreviousPremiumGrade());
            request.setPreviousPolicy(previousPolicyRequest);
        }
        return request;
    }

    private boolean isMneRenewStep(BasicUiModel basicUiModel, Optional<TrailContext> context) {
        return context.map(trailContext -> TrailStepEnum.valueOf(trailContext.getStep()))
            .orElse(null) == TrailStepEnum.BASIC_INFO
            && basicUiModel.getScenario() == ScenarioTypeEnum.renew && basicUiModel instanceof MontenegroUiModel;
    }

    private void populateAddress(CustomerRequest insured, Customer insuredSource) {
        if (Objects.nonNull(insuredSource.getIndividual()) && CollectionUtils.isNotEmpty(insuredSource.getIndividual().getAddresses())) {
            Address sourceAddress = insuredSource.getIndividual().getAddresses().get(0);
            convertAddress(insured, sourceAddress);
        }
        if (Objects.nonNull(insuredSource.getCompany()) && CollectionUtils.isNotEmpty(insuredSource.getCompany().getAddresses())) {
            Address sourceAddress = insuredSource.getCompany().getAddresses().get(0);
            convertAddress(insured, sourceAddress);
        }
    }

    private static void convertAddress(CustomerRequest insured, Address sourceAddress) {
        com.zatech.genesis.gateway.api.uosnbo.request.Address address = new com.zatech.genesis.gateway.api.uosnbo.request.Address();
        address.setAddressNo(sourceAddress.getAddress12());
        address.setStreet(sourceAddress.getAddress11());
        address.setZipCode(sourceAddress.getZipCode());
        address.setCity(sourceAddress.getAddress13());
        insured.setAddress(address);
    }

    public void handUosNboException(String message) {
        if (StringUtils.isNotBlank(message)) {
            throw CommonException.byErrorAndParams(UosNboErrorCode.VALIDATION_ERROR_CODE, message);
        }
        throw CommonException.byErrorAndParams(SystemErrorCodes.internal_server_error);
    }

    public String getUsername() {
        return userConfig.getUserName();
    }

    public String getAgentCode(OrderContext orderContext) {
        return userConfig.getAgentCode(orderContext);
    }

}